import { LibraryPage } from "~/components/pages/library/pages/LibraryPage.tsx";
import { mainCategoryKeySchema } from "~/components/pages/library/utils/filterContent.ts";
import { useLibraryDropdownStore } from "~/stores/libraryDropdown.store.ts";
import { api } from "~/utils/trpc.ts";

import { idValidation } from "@constellatio/schemas/common/common.validation";
import { createFileRoute } from "@tanstack/react-router";
import { zodValidator } from "@tanstack/zod-adapter";
import { z } from "zod";

const LibraryStartPage = () =>
{
  return <LibraryPage />;
};

export const Route = createFileRoute("/_authed/_onboarded/_subscribed/_legacy/library/")({
  component: LibraryStartPage,
  validateSearch: zodValidator(
    z
      .object({
        category: mainCategoryKeySchema.optional().default("Zivilrecht"),
        legalAreaId: idValidation.optional(),
        topicId: idValidation.optional(),
      })
      .refine(
        (data) =>
        {
          if (data.topicId && !data.legalAreaId)
          {
            return false;
          }
          return true;
        },
        { message: "topicId kann nur gesetzt werden, wenn legalAreaId gesetzt ist" }
      )
  ),
  beforeLoad: ({ search }) =>
  {
    const dropdownStore = useLibraryDropdownStore.getState();

    if (search.legalAreaId)
    {
      dropdownStore.openLegalArea(search.legalAreaId);

      if (search.topicId)
      {
        dropdownStore.openTopic(search.legalAreaId, search.topicId);
      }
    }

    return { search };
  },
  loader: async ({ context: { queryClient } }) =>
  {
    return Promise.all([
      queryClient.ensureQueryData(api.caisy.getAllLegalFields.queryOptions()),
      queryClient.ensureQueryData(api.caisy.getAllTopics.queryOptions()),
    ]);
  },
});
