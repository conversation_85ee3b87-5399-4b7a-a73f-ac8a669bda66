/* eslint-disable sort-keys-fix/sort-keys-fix,@typescript-eslint/naming-convention,@typescript-eslint/no-use-before-define,max-lines,import/no-deprecated */

import type { GameResultSchemaType } from "@constellatio/schemas/routers/gamesProgress/setGameProgressState.schema";
import type { SchemaFlashcardStep } from "@constellatio/shared/flashcards/types";
import {
  allBookmarkResourceTypes,
  allCaisyWebhookEventTypes,
  allCaseProgressStates,
  allGameProgressStates,
  allGenderIdentifiers,
  allOnboardingResults,
  allSearchIndexTypes,
  allSubscriptionStatuses,
  authProviders,
  badgeIdentifiers,
  badgePublicationState,
  contentItemViewsTypes,
  documentFileExtensions,
  documentFileMimeTypes,
  fileExtensions,
  fileMimeTypes,
  imageFileExtensions,
  imageFileMimeTypes,
  notificationTypesIdentifiers,
  profilePictureSources,
  roles,
  streakActivityTypes,
  subscriptionTypes,
  TrialPromoCodeStatus,
  userBadgeStates,
} from "@constellatio/shared/validation";
import { getCurrentDate } from "@constellatio/utils/dates";
import { type DeviceDetectorResult } from "device-detector-js";
import { type InferInsertModel, type InferSelectModel, relations, sql } from "drizzle-orm";
import {
  type AnyPgColumn,
  boolean,
  date,
  index,
  integer,
  jsonb,
  numeric,
  type PgColumn,
  pgEnum,
  pgTable,
  pgPolicy,
  type PgTable,
  primaryKey,
  serial,
  smallint,
  text,
  timestamp,
  unique,
  uniqueIndex,
  uuid,
  check,
  pgSchema,
  foreignKey,
  varchar,
} from "drizzle-orm/pg-core";
import { authenticatedRole, authUid } from "drizzle-orm/supabase";
import { createInsertSchema } from "drizzle-zod";

type InferPgSelectModel<T extends PgTable> = {
  columns: {
    [K in keyof T as T[K] extends PgColumn ? T[K]["_"]["name"] : never]: T[K] extends PgColumn
      ? T[K]["_"]["data"] | (T[K]["_"]["notNull"] extends true ? never : null)
      : never;
  };
  table: T["_"]["name"];
};

const authSchema = pgSchema("auth");
const constellatioUtilsSchema = pgSchema("constellatio_utils");

export const genderEnum = pgEnum("Gender", allGenderIdentifiers);
export const onboardingResultEnum = pgEnum("OnboardingResult", allOnboardingResults);
export const resourceTypeEnum = pgEnum("ResourceType", allBookmarkResourceTypes);
export const searchIndexTypeEnum = pgEnum("SearchIndexType", allSearchIndexTypes);
export const caisyWebhookEventTypeEnum = pgEnum("CaisyWebhookEventType", allCaisyWebhookEventTypes);
export const caseProgressStateEnum = pgEnum("CaseProgressState", allCaseProgressStates);
export const gameProgressStateEnum = pgEnum("GameProgressState", allGameProgressStates);
export const subscriptionStatusEnum = pgEnum("SubscriptionStatus", allSubscriptionStatuses);
export const imageFileExtensionEnum = pgEnum("ImageFileExtension", imageFileExtensions);
export const imageFileMimeTypeEnum = pgEnum("ImageFileMimeType", imageFileMimeTypes);
export const documentFileExtensionEnum = pgEnum("DocumentFileExtension", documentFileExtensions);
export const documentFileMimeTypeEnum = pgEnum("DocumentFileMimeType", documentFileMimeTypes);
export const fileExtensionEnum = pgEnum("FileExtension", fileExtensions);
export const fileMimeTypeEnum = pgEnum("FileMimeType", fileMimeTypes);
export const badgeIdentifierEnum = pgEnum("BadgeIdentifier", badgeIdentifiers);
export const userBadgeStateEnum = pgEnum("UserBadgeState", userBadgeStates);
export const badgePublicationStateEnum = pgEnum("BadgePublicationState", badgePublicationState);
export const roleEnum = pgEnum("Role", roles);
export const notificationTypeEnum = pgEnum("NotificationTypeEnum", notificationTypesIdentifiers);
export const streakActivityTypeEnum = pgEnum("StreakActivityType", streakActivityTypes);
export const contentItemViewTypeEnum = pgEnum("ContentItemViewType", contentItemViewsTypes);
export const authProviderEnum = pgEnum("AuthProvider", authProviders);
export const profilePictureSourceEnum = pgEnum("ProfilePictureSource", profilePictureSources);
export const flashcardStatusEnum = pgEnum("FlashcardStatus", ["poor", "average", "good"]);
export const TrialPromoCodeStatusEnum = pgEnum("TrialPromoCodeStatus", TrialPromoCodeStatus);
export const subscriptionTypeEnum = pgEnum("SubscriptionType", subscriptionTypes);
export const surveyTypeEnum = pgEnum("SurveyType", ["onboarding"]);

// TODO: Go through all queries and come up with useful indexes

/**
 * ------------------------ CAUTION ------------------------
 * The auth and constellatio_utils schema are not managed
 * by drizzle (see drizzle.config.ts). We need to manually
 * handle migrations for tables in these schemas.
 *
 * We still keep track of these tables here because of the
 * seed scripts where we insert data into the
 * 'environmentVariables' table and because of the foreign
 * key constraints in the 'users' table.
 */

export const auth_users = authSchema.table("users", {
  id: uuid("id").primaryKey(),
  email: text("email").unique().notNull(),
});

export type AuthUserInsert = InferInsertModel<typeof auth_users>;

export const constellatioUtils_environmentVariables = constellatioUtilsSchema.table("EnvironmentVariables", {
  key: constellatioUtilsSchema.enum("EnvironmentVariableKey", ["webhook_url", "webhook_headers"])("Key").primaryKey(),
  value: text("Value").notNull(),
  description: text("Description").notNull(),
  type: constellatioUtilsSchema
    .enum("EnvironmentVariableType", ["string", "number", "boolean", "array", "object"])("Type")
    .notNull(),
  createdAt: timestamp("CreatedAt").defaultNow().notNull(),
  updatedAt: timestamp("UpdatedAt").defaultNow().notNull(),
});

export type EnvironmentVariableInsert = InferInsertModel<typeof constellatioUtils_environmentVariables>;

/**
 * ----------- All tables below belong to the public schema and are managed by drizzle ----------
 */

export const users = pgTable("User", {
  id: uuid("Id")
    .primaryKey()
    .references(() => auth_users.id, { onDelete: "no action" }),
  authProvider: authProviderEnum("AuthProvider").notNull(),
  email: text("Email").unique().notNull(),
  displayName: text("DisplayName").notNull(),
  firstName: text("FirstName"),
  gender: genderEnum("Gender"),
  lastName: text("LastName"),
  semester: smallint("Semester"),
  stripeCustomerId: text("StripeCustomerId"),
  university: text("University"),
  externalAuthorityUrl: text("ExternalAuthorityUrl"),
  externalAuthorityDisplayName: text("ExternalAuthorityDisplayName"),
  onboardingResult: onboardingResultEnum("OnboardingResult"),
  signupDeviceInfo: jsonb("SignupDeviceInfo").$type<DeviceDetectorResult>(),
  subscriptionStatus: subscriptionStatusEnum("SubscriptionStatus"),
  subscriptionId: text("SubscriptionId"),
  hasSeenLearningPathsExplanation: boolean("HasSeenLearningPathsExplanation").default(false).notNull(),

  // Last-Touch UTM Parameters
  utm_source: text("utm_source"),
  utm_medium: text("utm_medium"),
  utm_campaign: text("utm_campaign"),
  utm_term: text("utm_term"),
  utm_content: text("utm_content"),
  utm_id: text("utm_id"),

  // First-Touch UTM Parameters
  first_touch_utm: jsonb("first_touch_utm"),
}).enableRLS();

export const usersRelations = relations(users, ({ many }) => ({
  notifications: many(notifications),
  profilePictures: many(profilePictures),
  usersToBadges: many(usersToBadges),
  usersToRoles: many(usersToRoles),
  forumQuestions: many(forumQuestions),
  forumAnswers: many(forumAnswers),
  learningPathUnitCompletions: many(learningPathUnitCompletions),
  stripeSubscriptions: many(stripeSubscriptions),
  userOrders: many(userOrders),
}));

export type UserInsert = InferInsertModel<typeof users>;
export type User = InferSelectModel<typeof users>;
export type UserSql = InferPgSelectModel<typeof users>;

export const userOrders = pgTable(
  "UserOrder",
  {
    id: uuid("Id").defaultRandom().primaryKey(),
    userId: uuid("UserId")
      .references(() => users.id)
      .notNull(),
    subscriptionStatus: subscriptionStatusEnum("SubscriptionStatus"),
    subscriptionId: text("SubscriptionId"),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    updatedAt: timestamp("UpdatedAt").defaultNow().notNull().$onUpdate(getCurrentDate),
    expiresAt: timestamp("ExpiresAt"),
    subscriptionType: subscriptionTypeEnum("SubscriptionType").notNull(),
  },
  (table) => [
    index("UserOrder_UserId_FK_Index").on(table.userId),
    index("UserOrder_UserId_SubscriptionStatus_ExpiresAt_Index").on(
      table.userId,
      table.subscriptionStatus,
      table.expiresAt
    ),
    index("UserOrder_UserId_SubscriptionId_Index").on(table.userId, table.subscriptionId),
  ]
).enableRLS();

export const userOrdersRelations = relations(userOrders, ({ one }) => ({
  user: one(users, { fields: [userOrders.userId], references: [users.id] }),
}));

export type UserOrderInsert = InferInsertModel<typeof userOrders>;
export type UserOrder = InferSelectModel<typeof userOrders>;
export type UserOrderSql = InferPgSelectModel<typeof userOrders>;

export const tags = pgTable(
  "Tag",
  {
    id: uuid("Id").primaryKey(),
    name: text("Name").notNull(),
    isShownInitiallyBeforeSearch: boolean("IsShownInitiallyBeforeSearch").notNull(),
    userId: uuid("UserId").references(() => users.id, {
      onDelete: "no action",
    }),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    updatedAt: timestamp("UpdatedAt").defaultNow().notNull().$onUpdate(getCurrentDate),
  },
  (table) => [index("Tag_UserId_FK_Index").on(table.userId)]
).enableRLS();

export const tagInsertSchema = createInsertSchema(tags);

export type TagInsert = InferInsertModel<typeof tags>;
export type Tag = InferSelectModel<typeof tags>;
export type TagSql = InferPgSelectModel<typeof tags>;

export const legalFields = pgTable("LegalField", {
  id: uuid("Id").primaryKey(),
  slug: text("Slug").notNull(),
  name: text("Name").notNull(),
  icon: text("Icon").notNull(),
  createdAt: timestamp("CreatedAt").defaultNow().notNull(),
  updatedAt: timestamp("UpdatedAt").defaultNow().notNull().$onUpdate(getCurrentDate),
}).enableRLS();

export const legalFieldInsertSchema = createInsertSchema(legalFields);

export type LegalFieldInsert = InferInsertModel<typeof legalFields>;
export type LegalField = InferSelectModel<typeof legalFields>;
export type LegalFieldSql = InferPgSelectModel<typeof legalFields>;

export const subFields = pgTable("SubField", {
  id: uuid("Id").primaryKey(),
  name: text("Name").notNull(),
  sorting: integer("Sorting"),
  createdAt: timestamp("CreatedAt").defaultNow().notNull(),
  updatedAt: timestamp("UpdatedAt").defaultNow().notNull().$onUpdate(getCurrentDate),
}).enableRLS();

export const subFieldInsertSchema = createInsertSchema(subFields);

export type SubFieldInsert = InferInsertModel<typeof subFields>;
export type SubField = InferSelectModel<typeof subFields>;
export type SubFieldSql = InferPgSelectModel<typeof subFields>;

export const topics = pgTable("Topic", {
  id: uuid("Id").primaryKey(),
  name: text("Name").notNull(),
  sorting: integer("Sorting"),
  createdAt: timestamp("CreatedAt").defaultNow().notNull(),
  updatedAt: timestamp("UpdatedAt").defaultNow().notNull().$onUpdate(getCurrentDate),
}).enableRLS();

export const topicInsertSchema = createInsertSchema(topics);

export type TopicInsert = InferInsertModel<typeof topics>;
export type Topic = InferSelectModel<typeof topics>;
export type TopicSql = InferPgSelectModel<typeof topics>;

/*
 * This table is used to store profile pictures for users.
 * There are two types of profile pictures:
 * 1. Internal profile pictures. These are profile pictures that were uploaded by the user to our own cloud storage.
 * 2. External profile pictures. These are the original profile pictures from the social auth provider.
 * Be careful because some columns like serverFilename cannot be null when the profile picture source is internal.
 */
export const profilePictures = pgTable(
  "ProfilePicture",
  {
    id: uuid("Id").primaryKey(),
    serverFilename: text("ServerFilename"),
    url: text("Url"),
    fileExtension: imageFileExtensionEnum("FileExtension"),
    contentType: imageFileMimeTypeEnum("ContentType"),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull()
      .unique(),
    profilePictureSource: profilePictureSourceEnum("ProfilePictureSource").notNull(),
  },
  (table) => [index("ProfilePicture_UserId_FK_Index").on(table.userId)]
).enableRLS();

export const profilePicturesRelations = relations(profilePictures, ({ one }) => ({
  user: one(users, {
    fields: [profilePictures.userId],
    references: [users.id],
  }),
}));

export type ProfilePictureInsert = InferInsertModel<typeof profilePictures>;
export type ProfilePicture = InferSelectModel<typeof profilePictures>;
export type ProfilePictureSql = InferPgSelectModel<typeof profilePictures>;

export const bookmarks = pgTable(
  "Bookmark",
  {
    id: uuid("Id").defaultRandom().primaryKey(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    resourceType: resourceTypeEnum("ResourceType").notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    resourceId: uuid("ResourceId").notNull(),
  },
  (table) => ({
    userId_resourceType_resourceId_index: index("Bookmark_UserId_ResourceType_ResourceId_Index").on(table.userId),
    userId_resourceType_resourceId_unique: unique().on(table.userId, table.resourceType, table.resourceId),
  })
).enableRLS();

export type BookmarkInsert = InferInsertModel<typeof bookmarks>;
export type Bookmark = InferSelectModel<typeof bookmarks>;
export type BookmarkSql = InferPgSelectModel<typeof bookmarks>;

export const uploadFolders = pgTable(
  "UploadFolder",
  {
    id: uuid("Id").defaultRandom().primaryKey(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    name: text("Name").notNull(),
  },
  (table) => [index("UploadFolder_UserId_FK_Index").on(table.userId)]
).enableRLS();

export type UploadFolderInsert = InferInsertModel<typeof uploadFolders>;
export type UploadFolder = InferSelectModel<typeof uploadFolders>;
export type UploadFolderSql = InferPgSelectModel<typeof uploadFolders>;

export const uploadedFiles = pgTable(
  "UploadedFile",
  {
    id: uuid("Id").defaultRandom().primaryKey(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    folderId: uuid("FolderId").references(() => uploadFolders.id, {
      onDelete: "no action",
    }),
    serverFilename: text("ServerFilename").notNull(),
    originalFilename: text("OriginalFilename").notNull(),
    sizeInBytes: integer("SizeInBytes").notNull(),
    fileExtension: fileExtensionEnum("FileExtension").notNull(),
    contentType: fileMimeTypeEnum("ContentType").notNull(),
  },
  (table) => [
    index("UploadedFile_UserId_FK_Index").on(table.userId),
    index("UploadedFile_FolderId_FK_Index").on(table.folderId),
  ]
).enableRLS();

export const uploadedFilesRelations = relations(uploadedFiles, ({ many }) => ({
  tags: many(uploadedFilesToTags),
}));

export type UploadedFileInsert = InferInsertModel<typeof uploadedFiles>;
export type UploadedFile = InferSelectModel<typeof uploadedFiles>;
export type UploadedFileSql = InferPgSelectModel<typeof uploadedFiles>;
export type UploadedFileWithTags = UploadedFile & {
  tags: Array<{ tagId: string }>;
};

export const documents = pgTable(
  "Document",
  {
    id: uuid("Id").defaultRandom().primaryKey(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    updatedAt: timestamp("UpdatedAt").defaultNow().notNull().$onUpdate(getCurrentDate),
    folderId: uuid("FolderId").references(() => uploadFolders.id, {
      onDelete: "no action",
    }),
    name: text("Name").notNull(),
    content: text("Content").notNull(),
  },
  (table) => [
    index("Document_UserId_FK_Index").on(table.userId),
    index("Document_FolderId_FK_Index").on(table.folderId),
  ]
).enableRLS();

export const documentsRelations = relations(documents, ({ many }) => ({
  tags: many(documentsToTags),
}));

export type DocumentInsert = InferInsertModel<typeof documents>;
export type Document = InferSelectModel<typeof documents>;
export type DocumentSql = InferPgSelectModel<typeof documents>;
export type DocumentWithTags = Document & { tags: Array<{ tagId: string }> };

export const notes = pgTable(
  "Note",
  {
    id: uuid("Id").defaultRandom().primaryKey(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    fileId: uuid("FileId")
      .references(() => uploadedFiles.id, { onDelete: "no action" })
      .notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    updatedAt: timestamp("UpdatedAt").defaultNow().notNull().$onUpdate(getCurrentDate),
    content: text("Content").notNull(),
  },
  (table) => [index("Note_UserId_FK_Index").on(table.userId), index("Note_FileId_FK_Index").on(table.fileId)]
).enableRLS();

export type NoteInsert = InferInsertModel<typeof notes>;
export type Note = InferSelectModel<typeof notes>;
export type NoteSql = InferPgSelectModel<typeof notes>;

export const casesProgress = pgTable(
  "CaseProgress",
  {
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    caseId: uuid("CaseId").notNull(),
    progressState: caseProgressStateEnum("ProgressState").notNull().default("not-started"),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    updatedAt: timestamp("UpdatedAt").defaultNow().notNull().$onUpdate(getCurrentDate),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.userId, table.caseId] }),
    userIdIndex: index("CaseProgress_UserId_Index").on(table.userId),
  })
).enableRLS();

export type CaseProgressInsert = InferInsertModel<typeof casesProgress>;
export type CaseProgress = InferSelectModel<typeof casesProgress>;
export type CaseProgressSql = InferPgSelectModel<typeof casesProgress>;

export const casesSolutions = pgTable(
  "CaseSolution",
  {
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    caseId: uuid("CaseId").notNull(),
    solution: text("Solution").notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.userId, table.caseId] }),
    userIdIndex: index("CaseSolution_UserId_Index").on(table.userId),
  })
).enableRLS();

export type CaseSolutionInsert = InferInsertModel<typeof casesSolutions>;
export type CaseSolution = InferSelectModel<typeof casesSolutions>;
export type CaseSolutionSql = InferPgSelectModel<typeof casesSolutions>;

export const gamesProgress = pgTable(
  "GameProgress",
  {
    id: serial("Id").primaryKey(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    gameId: uuid("GameId").notNull(),
    progressState: gameProgressStateEnum("ProgressState").notNull().default("not-started"),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    gameResult: jsonb("GameResult").$type<GameResultSchemaType>(),
    wasSolvedCorrectly: boolean("WasSolvedCorrectly"),
  },
  (table) => [index("GameProgress_UserId_GameId_Index").on(table.userId, table.gameId)]
).enableRLS();

export type GameProgressInsert = InferInsertModel<typeof gamesProgress>;
export type GameProgress = InferSelectModel<typeof gamesProgress>;
export type GameProgressSql = InferPgSelectModel<typeof gamesProgress>;

export const searchIndexUpdateQueue = pgTable(
  "SearchIndexUpdateQueue",
  {
    cmsId: uuid("CmsId").notNull(),
    searchIndexType: searchIndexTypeEnum("SearchIndexType").notNull(),
    eventType: caisyWebhookEventTypeEnum("EventType").notNull(),
  },
  (table) => ({
    pk: primaryKey({
      columns: [table.cmsId, table.searchIndexType, table.eventType],
    }),
  })
).enableRLS();

export type SearchIndexUpdateQueueInsert = InferInsertModel<typeof searchIndexUpdateQueue>;
export type SearchIndexUpdateQueueItem = InferSelectModel<typeof searchIndexUpdateQueue>;
export type SearchIndexUpdateQueueItemSql = InferPgSelectModel<typeof searchIndexUpdateQueue>;

export const badges = pgTable("Badge", {
  id: uuid("Id").defaultRandom().primaryKey(),
  identifier: badgeIdentifierEnum("Identifier").notNull().unique(),
  name: text("Name").notNull(),
  description: text("Description").notNull(),
  imageFilename: text("ImageFilename").notNull(),
  publicationState: badgePublicationStateEnum("PublicationState").notNull().default("not-listed"),
}).enableRLS();

export const badgesRelations = relations(badges, ({ many }) => ({
  usersToBadges: many(usersToBadges),
}));

export type BadgeInsert = InferInsertModel<typeof badges>;
export type Badge = InferSelectModel<typeof badges>;
export type BadgeSql = InferPgSelectModel<typeof badges>;
export type BadgeWithUserData = Badge & {
  isCompleted: boolean;
  wasSeen: boolean;
};

export const usersToBadges = pgTable(
  "User_to_Badge",
  {
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    badgeId: uuid("BadgeId")
      .references(() => badges.id, { onDelete: "no action" })
      .notNull(),
    userBadgeState: userBadgeStateEnum("UserBadgeState").default("not-seen").notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
  },
  (table) => [
    primaryKey({ columns: [table.userId, table.badgeId] }),
    pgPolicy("usersToBadges_read_access_for_users_own_badges", {
      as: "permissive",
      for: "select",
      to: authenticatedRole,
      using: sql`${table.userId} = ${authUid}`,
    }),
    index("User_to_Badge_UserId_FK_Index").on(table.userId),
    index("User_to_Badge_BadgeId_FK_Index").on(table.badgeId),
  ]
).enableRLS();

export const usersToBadgesRelations = relations(usersToBadges, ({ one }) => ({
  badge: one(badges, {
    fields: [usersToBadges.badgeId],
    references: [badges.id],
  }),
  user: one(users, {
    fields: [usersToBadges.userId],
    references: [users.id],
  }),
}));

export const forumQuestions = pgTable(
  "ForumQuestion",
  {
    id: uuid("Id").defaultRandom().primaryKey(),
    index: serial("Index"),
    slug: text("Slug").notNull(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    updatedAt: timestamp("UpdatedAt").defaultNow().notNull().$onUpdate(getCurrentDate),
    title: text("Title").notNull(),
    text: text("Text").notNull(),
  },
  (table) => ({
    id_slug_index: uniqueIndex("ForumQuestion_Id_Slug_Index").on(table.id, table.slug),
    forumQuestions_userId_fk_index: index("ForumQuestion_UserId_FK_Index").on(table.userId),
  })
).enableRLS();

export const forumQuestionsRelations = relations(forumQuestions, ({ many, one }) => ({
  forumQuestionToLegalFields: many(forumQuestionsToLegalFields),
  forumQuestionToSubfields: many(forumQuestionToSubfields),
  forumQuestionToTopics: many(forumQuestionToTopics),
  answers: many(forumAnswers),
  upvotes: many(questionUpvotes),
  user: one(users, {
    fields: [forumQuestions.userId],
    references: [users.id],
    relationName: "author",
  }),
}));

export type ForumQuestionInsert = InferInsertModel<typeof forumQuestions>;
export type ForumQuestion = InferSelectModel<typeof forumQuestions>;
export type ForumQuestionSql = InferPgSelectModel<typeof forumQuestions>;

export const forumQuestionsToLegalFields = pgTable(
  "ForumQuestion_to_LegalField",
  {
    questionId: uuid("QuestionId")
      .references(() => forumQuestions.id, { onDelete: "cascade" })
      .notNull(),
    legalFieldId: uuid("LegalFieldId").notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.questionId, table.legalFieldId] }),
    questionIdIndex: index("ForumQuestion_to_LegalField_QuestionId_idx").on(table.questionId),
  })
).enableRLS();

export const forumQuestionsToLegalFieldsRelations = relations(forumQuestionsToLegalFields, ({ one }) => ({
  question: one(forumQuestions, {
    fields: [forumQuestionsToLegalFields.questionId],
    references: [forumQuestions.id],
  }),
}));

export type ForumQuestionToLegalFieldInsert = InferInsertModel<typeof forumQuestionsToLegalFields>;
export type ForumQuestionToLegalField = InferSelectModel<typeof forumQuestionsToLegalFields>;
export type ForumQuestionToLegalFieldSql = InferPgSelectModel<typeof forumQuestionsToLegalFields>;

export const forumQuestionToSubfields = pgTable(
  "ForumQuestion_to_Subfield",
  {
    questionId: uuid("QuestionId")
      .references(() => forumQuestions.id, { onDelete: "cascade" })
      .notNull(),
    subfieldId: uuid("SubfieldId").notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.questionId, table.subfieldId] }),
    questionIdIndex: index("ForumQuestion_to_Subfield_QuestionId_idx").on(table.questionId),
  })
).enableRLS();

export const forumQuestionToSubfieldsRelations = relations(forumQuestionToSubfields, ({ one }) => ({
  question: one(forumQuestions, {
    fields: [forumQuestionToSubfields.questionId],
    references: [forumQuestions.id],
  }),
}));

export type ForumQuestionToSubfieldInsert = InferInsertModel<typeof forumQuestionToSubfields>;
export type ForumQuestionToSubfield = InferSelectModel<typeof forumQuestionToSubfields>;
export type ForumQuestionToSubfieldSql = InferPgSelectModel<typeof forumQuestionToSubfields>;

export const forumQuestionToTopics = pgTable(
  "ForumQuestion_to_Topic",
  {
    questionId: uuid("QuestionId")
      .references(() => forumQuestions.id, { onDelete: "cascade" })
      .notNull(),
    topicId: uuid("TopicId").notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.questionId, table.topicId] }),
    questionIdIndex: index("ForumQuestion_to_Topic_QuestionId_idx").on(table.questionId),
  })
).enableRLS();

export const forumQuestionToTopicsRelations = relations(forumQuestionToTopics, ({ one }) => ({
  question: one(forumQuestions, {
    fields: [forumQuestionToTopics.questionId],
    references: [forumQuestions.id],
  }),
}));

export type ForumQuestionToTopicInsert = InferInsertModel<typeof forumQuestionToTopics>;
export type ForumQuestionToTopic = InferSelectModel<typeof forumQuestionToTopics>;
export type ForumQuestionToTopicSql = InferPgSelectModel<typeof forumQuestionToTopics>;

export const forumAnswers = pgTable(
  "ForumAnswer",
  {
    id: uuid("Id").defaultRandom().primaryKey(),
    index: serial("Index"),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    updatedAt: timestamp("UpdatedAt").defaultNow().notNull().$onUpdate(getCurrentDate),
    text: text("AnswerText").notNull(),
    parentQuestionId: uuid("ParentQuestionId").references(() => forumQuestions.id, { onDelete: "no action" }),
    parentAnswerId: uuid("ParentAnswerId").references((): AnyPgColumn => forumAnswers.id, { onDelete: "no action" }),
  },
  (table) => [
    index("ForumAnswer_UserId_FK_Index").on(table.userId),
    index("ForumAnswer_ParentQuestionId_FK_Index").on(table.parentQuestionId),
    index("ForumAnswer_ParentAnswerId_FK_Index").on(table.parentAnswerId),
  ]
).enableRLS();

export const forumAnswersRelations = relations(forumAnswers, ({ one }) => ({
  user: one(users, {
    fields: [forumAnswers.userId],
    references: [users.id],
    relationName: "author",
  }),
  question: one(forumQuestions, {
    fields: [forumAnswers.parentQuestionId],
    references: [forumQuestions.id],
  }),
}));

export type ForumAnswerInsert = InferInsertModel<typeof forumAnswers>;
export type ForumAnswer = InferSelectModel<typeof forumAnswers>;
export type ForumAnswerSql = InferPgSelectModel<typeof forumAnswers>;

export const correctAnswers = pgTable(
  "CorrectAnswer",
  {
    id: uuid("Id").defaultRandom().primaryKey(),
    confirmedAt: timestamp("ConfirmedAt").defaultNow().notNull(),
    confirmedByUserId: uuid("ConfirmedByUserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    questionId: uuid("QuestionId")
      .references(() => forumQuestions.id, { onDelete: "cascade" })
      .notNull(),
    answerId: uuid("AnswerId")
      .references(() => forumAnswers.id, { onDelete: "cascade" })
      .unique()
      .notNull(),
  },
  (table) => [
    index("CorrectAnswer_UserId_FK_Index").on(table.confirmedByUserId),
    index("CorrectAnswer_QuestionId_FK_Index").on(table.questionId),
    index("CorrectAnswer_AnswerId_FK_Index").on(table.answerId),
  ]
).enableRLS();

export type CorrectAnswerInsert = InferInsertModel<typeof correctAnswers>;
export type CorrectAnswer = InferSelectModel<typeof correctAnswers>;
export type CorrectAnswerSql = InferPgSelectModel<typeof correctAnswers>;

export const questionUpvotes = pgTable(
  "QuestionUpvote",
  {
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    questionId: uuid("QuestionId")
      .references(() => forumQuestions.id, { onDelete: "cascade" })
      .notNull(),
    createdAt: timestamp("CreatedAt").defaultNow(),
  },
  (table) => ({
    questionId_index: index("QuestionUpvote_QuestionId_Index").on(table.questionId),
    userId_index: index("QuestionUpvote_UserId_Index").on(table.userId),
    pk: primaryKey({ columns: [table.userId, table.questionId] }),
  })
).enableRLS();

export const questionUpvotesRelations = relations(questionUpvotes, ({ one }) => ({
  question: one(forumQuestions, {
    fields: [questionUpvotes.questionId],
    references: [forumQuestions.id],
  }),
}));

export type QuestionUpvoteInsert = InferInsertModel<typeof questionUpvotes>;
export type QuestionUpvote = InferSelectModel<typeof questionUpvotes>;
export type QuestionUpvoteSql = InferPgSelectModel<typeof questionUpvotes>;

export const answerUpvotes = pgTable(
  "AnswerUpvote",
  {
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    answerId: uuid("AnswerId")
      .references(() => forumAnswers.id, { onDelete: "cascade" })
      .notNull(),
    createdAt: timestamp("CreatedAt").defaultNow(),
  },
  (table) => ({
    answerId_index: index("AnswerUpvote_QuestionId_Index").on(table.answerId),
    userId_index: index("AnswerUpvote_UserId_Index").on(table.userId),
    pk: primaryKey({ columns: [table.userId, table.answerId] }),
  })
).enableRLS();

export type AnswerUpvoteInsert = InferInsertModel<typeof answerUpvotes>;
export type AnswerUpvote = InferSelectModel<typeof answerUpvotes>;
export type AnswerUpvoteSql = InferPgSelectModel<typeof answerUpvotes>;

export const userRoles = pgTable(
  "UserRole",
  {
    id: uuid("Id").defaultRandom().primaryKey(),
    identifier: roleEnum("Identifier").unique().notNull(),
    name: text("Name").notNull(),
    description: text("Description").notNull(),
  },
  (table) => ({
    role_index: index("UserRole_Role_Index").on(table.identifier),
  })
).enableRLS();

export const userRolesRelations = relations(userRoles, ({ many }) => ({
  usersToRoles: many(usersToRoles),
}));

export type UserRoleInsert = InferInsertModel<typeof userRoles>;
export type UserRole = InferSelectModel<typeof userRoles>;
export type UserRoleSql = InferPgSelectModel<typeof userRoles>;

export const usersToRoles = pgTable(
  "User_to_Role",
  {
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    roleId: uuid("RoleId")
      .references(() => userRoles.id, { onDelete: "no action" })
      .notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.userId, table.roleId] }),
    usersToRoles_userId_fk_index: index("User_to_Role_UserId_FK_Index").on(table.userId),
    usersToRoles_roleId_fk_index: index("User_to_Role_RoleId_FK_Index").on(table.roleId),
  })
).enableRLS();

export const usersToRolesRelations = relations(usersToRoles, ({ one }) => ({
  role: one(userRoles, {
    fields: [usersToRoles.roleId],
    references: [userRoles.id],
  }),
  user: one(users, {
    fields: [usersToRoles.userId],
    references: [users.id],
  }),
}));

export type UserToRoleInsert = InferInsertModel<typeof usersToRoles>;
export type UserToRole = InferSelectModel<typeof usersToRoles>;
export type UserToRoleSql = InferPgSelectModel<typeof usersToRoles>;

export const notificationTypes = pgTable("NotificationType", {
  identifier: notificationTypeEnum("NotificationType").primaryKey(),
  name: text("Name").notNull(),
  description: text("Description").notNull(),
}).enableRLS();

export const notificationTypesRelations = relations(notificationTypes, ({ many }) => ({
  notifications: many(notifications),
}));

export type NotificationTypeInsert = InferInsertModel<typeof notificationTypes>;
export type NotificationType = InferSelectModel<typeof notificationTypes>;
export type NotificationTypeSql = InferPgSelectModel<typeof notificationTypes>;

export const notifications = pgTable(
  "Notification",
  {
    id: uuid("Id").defaultRandom().primaryKey(),
    index: serial("Index"),
    recipientId: uuid("RecipientId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    senderId: uuid("SenderId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    resourceId: uuid("ResourceId"),
    typeIdentifier: notificationTypeEnum("NotificationType").notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    readAt: timestamp("ReadAt"),
  },
  (table) => [
    check("sender_recipient_different", sql`${table.senderId} != ${table.recipientId}`),
    foreignKey({
      columns: [table.typeIdentifier],
      foreignColumns: [notificationTypes.identifier],
      name: "Notification_NotificationType_fk",
    }).onDelete("no action"),
    pgPolicy("notifications_read_access_for_users_own_notifications", {
      as: "permissive",
      for: "select",
      to: authenticatedRole,
      using: sql`${table.recipientId} = ${authUid}`,
    }),
    index("Notification_RecipientId_FK_Index").on(table.recipientId),
    index("Notification_SenderId_FK_Index").on(table.senderId),
    index("Notification_ResourceId_FK_Index").on(table.resourceId),
    index("Notification_TypeIdentifier_FK_Index").on(table.typeIdentifier),
  ]
).enableRLS();

export const notificationsRelations = relations(notifications, ({ one }) => ({
  notificationType: one(notificationTypes, {
    fields: [notifications.typeIdentifier],
    references: [notificationTypes.identifier],
  }),
  recipient: one(users, {
    fields: [notifications.recipientId],
    references: [users.id],
  }),
  sender: one(users, {
    fields: [notifications.senderId],
    references: [users.id],
  }),
}));

export type NotificationInsert = InferInsertModel<typeof notifications>;
export type Notification = InferSelectModel<typeof notifications>;
export type NotificationSql = InferPgSelectModel<typeof notifications>;

export const pings = pgTable(
  "Ping",
  {
    index: serial("Index").primaryKey(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
    path: text("Path").notNull(),
    search: text("Search"),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    pingInterval: smallint("PingInterval").notNull(),
    deviceInfo: jsonb("DeviceInfo").$type<DeviceDetectorResult>(),
  },
  (table) => [
    index("Ping_Path_Index").on(table.path),
    index("Ping_CreatedAt_Index").on(table.createdAt),
    index("Ping_UserId_FK_Index").on(table.userId),
  ]
).enableRLS();

export type PingInsert = InferInsertModel<typeof pings>;
export type Ping = InferSelectModel<typeof pings>;
export type PingSql = InferPgSelectModel<typeof pings>;

export const documentsToTags = pgTable(
  "Document_to_Tag",
  {
    documentId: uuid("DocumentId")
      .references(() => documents.id, { onDelete: "cascade" })
      .notNull(),
    tagId: uuid("TagId").notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.documentId, table.tagId] }),
    documentId_index: index("Document_to_Tag_DocumentId_Index").on(table.documentId),
  })
).enableRLS();

export const documentsToTagsRelations = relations(documentsToTags, ({ one }) => ({
  document: one(documents, {
    fields: [documentsToTags.documentId],
    references: [documents.id],
  }),
}));

export type DocumentToTagInsert = InferInsertModel<typeof documentsToTags>;
export type DocumentToTag = InferSelectModel<typeof documentsToTags>;
export type DocumentToTagSql = InferPgSelectModel<typeof documentsToTags>;

export const uploadedFilesToTags = pgTable(
  "UploadedFile_to_Tag",
  {
    fileId: uuid("FileId")
      .references(() => uploadedFiles.id, { onDelete: "cascade" })
      .notNull(),
    tagId: uuid("TagId").notNull(),
  },
  (table) => ({
    pk: primaryKey({ columns: [table.fileId, table.tagId] }),
    fileId_index: index("UploadedFile_to_Tag_FileId_Index").on(table.fileId),
  })
).enableRLS();

export const uploadedFilesToTagsRelations = relations(uploadedFilesToTags, ({ one }) => ({
  file: one(uploadedFiles, {
    fields: [uploadedFilesToTags.fileId],
    references: [uploadedFiles.id],
  }),
}));

export type UploadedFileToTagInsert = InferInsertModel<typeof uploadedFilesToTags>;
export type UploadedFileToTag = InferSelectModel<typeof uploadedFilesToTags>;
export type UploadedFileToTagSql = InferPgSelectModel<typeof uploadedFilesToTags>;

export const referralCodes = pgTable(
  "ReferralCode",
  {
    code: text("Code").primaryKey(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
  },
  (table) => [index("ReferralCode_UserId_FK_Index").on(table.userId)]
).enableRLS();

export type ReferralCodeInsert = InferInsertModel<typeof referralCodes>;
export type ReferralCode = InferSelectModel<typeof referralCodes>;
export type ReferralCodeSql = InferPgSelectModel<typeof referralCodes>;

export const referrals = pgTable(
  "Referral",
  {
    index: serial("Index").primaryKey(),
    code: text("Code").notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    referredUserId: uuid("ReferredUserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    referringUserId: uuid("ReferringUserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    paid: boolean("Paid").default(false).notNull(),
  },
  (table) => [
    index("Referral_UserId_FK_Index").on(table.referredUserId),
    index("Referral_ReferringUserId_FK_Index").on(table.referringUserId),
  ]
).enableRLS();

export type ReferralInsert = InferInsertModel<typeof referrals>;
export type Referral = InferSelectModel<typeof referrals>;
export type ReferralSql = InferPgSelectModel<typeof referrals>;

export const referralBalances = pgTable(
  "ReferralBalance",
  {
    index: serial("Index").primaryKey(),
    totalRefferalBonus: integer("TotalRefferalBonus").default(0).notNull(),
    paidOutRefferalBonus: integer("PaidOutRefferalBonus").default(0).notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
  },
  (table) => [index("ReferralBalance_UserId_FK_Index").on(table.userId)]
).enableRLS();

export type ReferralBalanceInsert = InferInsertModel<typeof referralBalances>;
export type ReferralBalance = InferSelectModel<typeof referralBalances>;
export type ReferralBalanceSql = InferPgSelectModel<typeof referralBalances>;

export const updateUserInCrmQueue = pgTable(
  "UpdateUserInCrmQueue",
  {
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "cascade" })
      .primaryKey(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
  },
  (table) => [index("UpdateUserInCrmQueue_UserId_Index").on(table.userId)]
).enableRLS();

export type UpdateUserInCrmQueueInsert = InferInsertModel<typeof updateUserInCrmQueue>;
export type UpdateUserInCrmQueue = InferSelectModel<typeof updateUserInCrmQueue>;

export const streak = pgTable(
  "Streak",
  {
    id: serial("id").primaryKey(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    startDate: date("StartDate", { mode: "date" }).defaultNow().notNull(),
    lastSatisfiedDate: date("LastSatisfiedDate", { mode: "date" }).defaultNow().notNull(),
    satisfiedDays: integer("SatisfiedDays").default(1),
    streakAlive: boolean("StreakAlive").default(true),
    lastCheckDate: date("LastCheckDate", { mode: "date" }).defaultNow().notNull(),
  },
  (table) => [index("Streak_UserId_FK_Index").on(table.userId)]
).enableRLS();

export type StreakInsert = InferInsertModel<typeof streak>;
export type Streak = InferSelectModel<typeof streak>;
export type StreakSql = InferPgSelectModel<typeof streak>;

export const streakActivities = pgTable(
  "StreakActivities",
  {
    id: serial("id").primaryKey(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
    activityType: streakActivityTypeEnum("ActivityType").notNull(),
    createdAt: date("CreatedAt", { mode: "date" }).defaultNow().notNull(),
  },
  (table) => [index("StreakActivity_UserId_FK_Index").on(table.userId)]
).enableRLS();

export type StreakActivityInsert = InferInsertModel<typeof streakActivities>;
export type StreakActivity = InferSelectModel<typeof streakActivities>;
export type StreakActivitySql = InferPgSelectModel<typeof streakActivities>;

export const contentViews = pgTable(
  "ContentView",
  {
    id: serial().primaryKey(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    contentItemId: uuid("ContentItemId").notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    contentItemType: contentItemViewTypeEnum("ContentItemType").notNull(),
  },
  (table) => ({
    contentItemId_index: index("ContentView_ContentItemId_Index").on(table.contentItemId),
    contentItemType_index: index("ContentView_ContentItemType_Index").on(table.contentItemType),
    contentViews_userId_fk_index: index("ContentView_UserId_FK_Index").on(table.userId),
  })
).enableRLS();

export type ContentViewInsert = InferInsertModel<typeof contentViews>;
export type ContentView = InferSelectModel<typeof contentViews>;
export type ContentViewSql = InferPgSelectModel<typeof contentViews>;

export const flashcardsSets = pgTable(
  "FlashcardsSet",
  {
    id: uuid("Id").primaryKey().defaultRandom(),
    index: serial("Index"),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    updatedAt: timestamp("UpdatedAt").defaultNow().notNull().$onUpdate(getCurrentDate),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    name: text("Name").notNull(),
    description: text("Description"),
    deletedAt: timestamp("DeletedAt"),
    deletedPermanentAt: timestamp("DeletedPermanentAt"),
  },
  (table) => [
    index("FlashcardsSet_Composite_Index").on(table.userId, table.deletedAt, table.updatedAt, table.deletedPermanentAt),
  ]
).enableRLS();

export const flashcardsSetsRelations = relations(flashcardsSets, ({ many }) => ({
  flashcardsToSets: many(flashcardsToSets),
  userStarredSets: many(userStarredSets),
}));

export type FlashcardsSetInsert = InferInsertModel<typeof flashcardsSets>;
export type FlashcardsSet = InferSelectModel<typeof flashcardsSets>;
export type FlashcardsSetSql = InferPgSelectModel<typeof flashcardsSets>;

export const clozeDeletionCards = pgTable(
  "ClozeDeletionCards",
  {
    id: uuid("Id").primaryKey().defaultRandom(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    content: text("Content").notNull(), // TODO: eventuell macht jsonB mehr sinn wegen dem sonder Format?
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
  },
  (table) => [index("ClozeDeletionCard_UserId_FK_Index").on(table.userId)]
).enableRLS();

export type ClozeDeletionCardInsert = InferInsertModel<typeof clozeDeletionCards>;
export type ClozeDeletionCard = InferSelectModel<typeof clozeDeletionCards>;
export type ClozeDeletionCardSql = InferPgSelectModel<typeof clozeDeletionCards>;

export const flashcards = pgTable(
  "Flashcard",
  {
    id: uuid("Id").primaryKey().defaultRandom(),
    index: serial("Index"),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    updatedAt: timestamp("UpdatedAt").defaultNow().notNull(),
    deletedAt: timestamp("DeletedAt"),
    deletedPermanentAt: timestamp("DeletedPermanentAt"),
    versionNumber: integer("VersionNumber").default(1).notNull(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "no action" })
      .notNull(),
    content: jsonb("Content")
      .$type<
        | {
            answer: string;
            question: string;
            type: "standard";
          }
        | {
            additionalInfo?: string;
            clozeDeletion: string;
            type: "clozeDeletion";
          }
        | {
            case?: { case: string; solution?: string };
            opinions: Array<{
              conArguments?: string;
              content: string;
              highlight: boolean;
              proArguments?: string;
              title: string;
            }>;
            problem: string;
            sourceNotes?: string;
            type: "problem";
          }
        | {
            legalConsequence?: string;
            steps: SchemaFlashcardStep[];
            title: string;
            type: "schema";
          }
      >()
      .notNull(),
    rawContent: text("RawContent").notNull(), // Für Volltextsuche
    clozeDeletionCardId: uuid("ClozeDeletionCardId").references(() => clozeDeletionCards.id, { onDelete: "no action" }),
    clozeDeletionIndex: integer("ClozeDeletionIndex"),
    // TODO: for realted cms content
    // cmsContentPiceId: uuid("CmsContentPieceId").references(() => <???>, { onDelete: "no action" }),
    // cmsContentType: enum (case / article)
    // blockId: uuid("BlockId").references(() => <???>, { onDelete: "no action" }),
    // Note: wenn cmsId dann auch content type.
  },
  (table) => [
    index("Flashcard_UserId_FK_Index").on(table.userId),
    index("Flashcard_RawContent_Index").on(table.rawContent),
    uniqueIndex("Flashcard_ClozeDeletion_Compound_Idx").on(table.clozeDeletionCardId, table.clozeDeletionIndex),
  ]
).enableRLS();

export const flashcardsRelations = relations(flashcards, ({ many, one }) => ({
  flashcardsToSets: many(flashcardsToSets),
  user: one(users, {
    fields: [flashcards.userId],
    references: [users.id],
  }),
  flashcardReviews: many(flashcardReviews),
  tags: many(flashcardsToTags),
}));

export type FlashcardInsert = InferInsertModel<typeof flashcards>;
export type Flashcard = InferSelectModel<typeof flashcards>;
export type FlashcardSql = InferPgSelectModel<typeof flashcards>;
export type FlashcardContent = Flashcard["content"];
export type FlashcardContentStandard = Extract<FlashcardContent, { type: "standard" }>;
export type FlashcardContentClozeDeletion = Extract<FlashcardContent, { type: "clozeDeletion" }>;
export type FlashcardContentProblem = Extract<FlashcardContent, { type: "problem" }>;
export type FlashcardContentSchema = Extract<FlashcardContent, { type: "schema" }>;

export const flashcardsToSets = pgTable(
  "Flashcard_to_Set",
  {
    flashcardId: uuid("FlashcardId")
      .references(() => flashcards.id, { onDelete: "cascade" })
      .notNull(),
    setId: uuid("SetId")
      .references(() => flashcardsSets.id, { onDelete: "cascade" })
      .notNull(),
  },
  (table) => [
    primaryKey({ columns: [table.flashcardId, table.setId] }),
    index("FlashcardsToSets_FlashcardId_FK_Index").on(table.flashcardId),
    index("FlashcardsToSets_SetId_FK_Index").on(table.setId),
  ]
).enableRLS();

export const flashcardsToSetsRelations = relations(flashcardsToSets, ({ one }) => ({
  flashcard: one(flashcards, {
    fields: [flashcardsToSets.flashcardId],
    references: [flashcards.id],
  }),
  set: one(flashcardsSets, {
    fields: [flashcardsToSets.setId],
    references: [flashcardsSets.id],
  }),
}));

export type FlashcardsToSetsInsert = InferInsertModel<typeof flashcardsToSets>;
export type FlashcardsToSets = InferSelectModel<typeof flashcardsToSets>;
export type FlashcardsToSetsSql = InferPgSelectModel<typeof flashcardsToSets>;

export const flashcardReviews = pgTable(
  "FlashcardReviews",
  {
    id: uuid("Id").primaryKey().defaultRandom(),
    index: serial("Index"),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    updatedAt: timestamp("UpdatedAt").defaultNow().notNull().$onUpdate(getCurrentDate),
    flashcardId: uuid("FlashcardId")
      .references(() => flashcards.id, { onDelete: "cascade" })
      .notNull(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
    due: timestamp("Due").defaultNow().notNull(),
    stability: numeric("Stability", {
      precision: 15,
      scale: 10,
    }).$type<number>(),
    difficulty: numeric("Difficulty", {
      precision: 15,
      scale: 10,
    }).$type<number>(),
    elapsed_days: integer("ElapsedDays").default(0).notNull(),
    scheduled_days: integer("ScheduledDays").default(0).notNull(),
    reps: integer("Reps").default(0).notNull(),
    lapses: integer("Lapses").default(0).notNull(),
    state: integer("State").default(0).notNull(), // State.New = 0, Learning = 1, Review = 2, Relearning = 3
    last_review: timestamp("LastReview"),
  },
  (table) => [
    index("FlashcardReviews_FlashcardId_FK_Index").on(table.flashcardId),
    index("FlashcardReviews_UserId_FK_Index").on(table.userId),
  ]
).enableRLS();

export const flashcardReviewsRelations = relations(flashcardReviews, ({ one }) => ({
  flashcard: one(flashcards, {
    fields: [flashcardReviews.flashcardId],
    references: [flashcards.id],
  }),
}));

export type FlashcardReviewInsert = InferInsertModel<typeof flashcardReviews>;
export type FlashcardReview = InferSelectModel<typeof flashcardReviews>;
export type FlashcardReviewSql = InferPgSelectModel<typeof flashcardReviews>;

export const flashcardReviewLogs = pgTable(
  "FlashcardReviewLog",
  {
    id: uuid("Id").primaryKey().defaultRandom(),
    index: serial("Index"),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    flashcardId: uuid("FlashcardId")
      .references(() => flashcards.id)
      .notNull(),
    userId: uuid("UserId")
      .references(() => users.id)
      .notNull(),
    rating: integer("Rating").notNull(), // Rating.Manual = 0, Again = 1, Hard = 2, Good = 3, Easy = 4
    state: integer("State").notNull(), // State.New = 0, Learning = 1, Review = 2, Relearning = 3
    due: timestamp("Due").notNull(),
    stability: numeric("Stability", {
      precision: 15,
      scale: 10,
    }).$type<number>(),
    difficulty: numeric("Difficulty", {
      precision: 15,
      scale: 10,
    }).$type<number>(),
    elapsed_days: integer("ElapsedDays").notNull(),
    last_elapsed_days: integer("LastElapsedDays"),
    scheduled_days: integer("ScheduledDays").notNull(),
    review: timestamp("Review").notNull(),
  },
  (table) => [
    index("FlashcardReviewLog_FlashcardId_FK_Index").on(table.flashcardId),
    index("FlashcardReviewLog_UserId_FK_Index").on(table.userId),
  ]
).enableRLS();

export const flashcardReviewLogsRelations = relations(flashcardReviewLogs, ({ one }) => ({
  flashcard: one(flashcards, {
    fields: [flashcardReviewLogs.flashcardId],
    references: [flashcards.id],
  }),
}));

export type FlashcardReviewLogInsert = InferInsertModel<typeof flashcardReviewLogs>;
export type FlashcardReviewLog = InferSelectModel<typeof flashcardReviewLogs>;
export type FlashcardReviewLogSql = InferPgSelectModel<typeof flashcardReviewLogs>;

export const userStarredSets = pgTable(
  "UserStarredSets",
  {
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
    setId: uuid("SetId")
      .references(() => flashcardsSets.id, { onDelete: "cascade" })
      .notNull(),
    starredAt: timestamp("StarredAt").defaultNow().notNull(),
  },
  (table) => [primaryKey({ columns: [table.userId, table.setId] })]
).enableRLS();

export const userStarredSetsRelations = relations(userStarredSets, ({ one }) => ({
  user: one(users, {
    fields: [userStarredSets.userId],
    references: [users.id],
  }),
  set: one(flashcardsSets, {
    fields: [userStarredSets.setId],
    references: [flashcardsSets.id],
  }),
}));

export const trialPromoCodes = pgTable("TrialPromoCodes", {
  id: uuid("Id").defaultRandom().primaryKey(),
  code: varchar("Code", { length: 30 }).unique(),
  createdAt: timestamp("CreatedAt").notNull().defaultNow(),
  amountOfTrialDays: integer("AmountOfTrialDays").notNull(),
  expirationDate: timestamp("ExpirationDate"),
  usageLimit: integer("UsageLimit"),
  status: TrialPromoCodeStatusEnum("Status").default("active").notNull(),
}).enableRLS();

export type TrialPromoCodeInsert = InferInsertModel<typeof trialPromoCodes>;
export type TrialPromoCode = InferSelectModel<typeof trialPromoCodes>;
export type TrialPromoCodeSql = InferPgSelectModel<typeof trialPromoCodes>;

export const trialPromoCodesToUsers = pgTable(
  "TrialPromoCode_to_User",
  {
    codeId: uuid("CodeId")
      .references(() => trialPromoCodes.id, { onDelete: "no action" })
      .notNull(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
  },
  (table) => [primaryKey({ columns: [table.codeId, table.userId] })]
).enableRLS();

export type TrialPromoCodeToUserInsert = InferInsertModel<typeof trialPromoCodesToUsers>;
export type TrialPromoCodeToUser = InferSelectModel<typeof trialPromoCodesToUsers>;
export type TrialPromoCodeToUserSql = InferPgSelectModel<typeof trialPromoCodesToUsers>;

export const flashcardsToTags = pgTable(
  "Flashcard_to_Tag",
  {
    flashcardId: uuid("FlashcardId")
      .references(() => flashcards.id, { onDelete: "cascade" })
      .notNull(),
    tagId: uuid("TagId")
      .references(() => tags.id, { onDelete: "cascade" })
      .notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
  },
  (table) => [primaryKey({ columns: [table.flashcardId, table.tagId] })]
).enableRLS();

export const flashcardsToTagsRelations = relations(flashcardsToTags, ({ one }) => ({
  flashcard: one(flashcards, {
    fields: [flashcardsToTags.flashcardId],
    references: [flashcards.id],
  }),
  tag: one(tags, {
    fields: [flashcardsToTags.tagId],
    references: [tags.id],
  }),
}));

export const tagsRelations = relations(tags, ({ many }) => ({
  flashcardsToTags: many(flashcardsToTags),
}));

export const flashcardsSetsToTags = pgTable(
  "FlashcardSet_to_Tag",
  {
    flashcardSetId: uuid("FlashcardSetId")
      .references(() => flashcardsSets.id, { onDelete: "cascade" })
      .notNull(),
    tagId: uuid("TagId")
      .references(() => tags.id, { onDelete: "cascade" })
      .notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
  },
  (table) => [primaryKey({ columns: [table.flashcardSetId, table.tagId] })]
).enableRLS();

export const flashcardsSetsToTagsRelations = relations(flashcardsSetsToTags, ({ one }) => ({
  flashcardSet: one(flashcardsSets, {
    fields: [flashcardsSetsToTags.flashcardSetId],
    references: [flashcardsSets.id],
  }),
}));

export const logins = pgTable("Login", {
  id: serial("id").primaryKey(),
  userId: uuid("UserId").notNull(),
  createdAt: timestamp("CreatedAt").defaultNow().notNull(),
}).enableRLS();

export type LoginInsert = InferInsertModel<typeof logins>;
export type Login = InferSelectModel<typeof logins>;
export type LoginSql = InferPgSelectModel<typeof logins>;

export const learningPathUnitCompletions = pgTable(
  "LearningPathUnitCompletion",
  {
    userId: uuid("UserId")
      .references(() => users.id)
      .notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    learningPathId: uuid("LearningPathId").notNull(),
    learningPathUnitTestId: uuid("LearningPathUnitTestId").notNull(),
    completedAt: timestamp("CompletedAt"),
  },
  (table) => [
    primaryKey({
      columns: [table.userId, table.learningPathId, table.learningPathUnitTestId],
    }),
    index("LearningPathUnitCompletion_UserId_LearningPathId_FK_Index").on(table.userId, table.learningPathId),
  ]
).enableRLS();

export const learningPathUnitCompletionsRelations = relations(learningPathUnitCompletions, ({ one }) => ({
  user: one(users, {
    fields: [learningPathUnitCompletions.userId],
    references: [users.id],
  }),
}));

export type LearningPathUnitCompletionInsert = InferInsertModel<typeof learningPathUnitCompletions>;
export type LearningPathUnitCompletion = InferSelectModel<typeof learningPathUnitCompletions>;
export type LearningPathUnitCompletionSql = InferPgSelectModel<typeof learningPathUnitCompletions>;

export const stripeSubscriptions = pgTable(
  "StripeSubscription",
  {
    id: uuid("Id").defaultRandom().primaryKey(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
    subscriptionId: text("SubscriptionId").notNull(),
    startDate: integer("StartDate").notNull(),
    endDate: integer("EndDate"),
    canceledAt: integer("CanceledAt"),
    cancelAt: integer("CancelAt"),
    cancelAtPeriodEnd: boolean("CancelAtPeriodEnd").notNull(),
    endedAt: integer("EndedAt"),
    planId: text("PlanId").notNull(),
    status: text("Status").notNull(),
    latestInvoiceId: text("LatestInvoiceId"),
  },
  (table) => [
    index("SubscriptionPayment_UserId_FK_Index").on(table.userId),
    index("SubscriptionPayment_SubscriptionId_Index").on(table.subscriptionId),
  ]
).enableRLS();

export type StripeSubscriptionInsert = InferInsertModel<typeof stripeSubscriptions>;
export type StripeSubscription = InferSelectModel<typeof stripeSubscriptions>;
export type StripeSubscriptionSql = InferPgSelectModel<typeof stripeSubscriptions>;

export const stripeSubscriptionsRelations = relations(stripeSubscriptions, ({ one }) => ({
  user: one(users, {
    fields: [stripeSubscriptions.userId],
    references: [users.id],
  }),
}));

export const stripeInvoices = pgTable(
  "StripeInvoice",
  {
    amountDue: integer("AmountDue").notNull(),
    amountPaid: integer("AmountPaid").notNull(),
    currency: text("Currency").notNull(),
    endDate: integer("EndDate"),
    id: uuid("Id").defaultRandom().primaryKey(),
    invoiceCreatedAt: integer("InvoiceCreatedAt").notNull(),
    invoiceId: text("InvoiceId").notNull(),
    planId: text("PlanId").notNull(),
    planPeriodEnd: integer("PlanPeriodEnd").notNull(),
    planPeriodStart: integer("PlanPeriodStart").notNull(),
    regularPlanPrice: integer("RegularPlanPrice").notNull(),
    status: text("Status").notNull(),
    totalSubscriptionDays: integer("TotalSubscriptionDays").notNull(),
    userId: uuid("UserId")
      .references(() => users.id)
      .notNull(),
  },
  (table) => [
    index("StripeInvoice_UserId_FK_Index").on(table.userId),
    index("StripeInvoice_InvoiceId_Index").on(table.invoiceId),
  ]
).enableRLS();

export type StripeInvoiceInsert = InferInsertModel<typeof stripeInvoices>;
export type StripeInvoice = InferSelectModel<typeof stripeInvoices>;
export type StripeInvoiceSql = InferPgSelectModel<typeof stripeInvoices>;

export const stripeInvoicesRelations = relations(stripeInvoices, ({ one }) => ({
  user: one(users, {
    fields: [stripeInvoices.userId],
    references: [users.id],
  }),
}));

export const seenSystemMessages = pgTable(
  "SeenSystemMessage",
  {
    messageId: uuid("MessageId").notNull(),
    userId: uuid("UserId")
      .references(() => users.id)
      .notNull(),
    seenAt: timestamp("SeenAt").defaultNow().notNull(),
  },
  (table) => [primaryKey({ columns: [table.messageId, table.userId] })]
).enableRLS();

export type SeenSystemMessageInsert = InferInsertModel<typeof seenSystemMessages>;
export type SeenSystemMessage = InferSelectModel<typeof seenSystemMessages>;
export type SeenSystemMessageSql = InferPgSelectModel<typeof seenSystemMessages>;

export const surveys = pgTable(
  "Survey",
  {
    id: uuid("Id").defaultRandom().primaryKey(),
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
    type: surveyTypeEnum("Type").notNull(),
    content: jsonb("Content").notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    inviteeUrl: text("InviteeUrl"),
  },
  (table) => [index("Survey_UserId_Type_Index").on(table.userId, table.type)]
).enableRLS();

export type SurveyInsert = InferInsertModel<typeof surveys>;
export type Survey = InferSelectModel<typeof surveys>;
export type SurveySql = InferPgSelectModel<typeof surveys>;

export const articlesReads = pgTable(
  "ArticleRead",
  {
    userId: uuid("UserId")
      .references(() => users.id, { onDelete: "cascade" })
      .notNull(),
    articleId: uuid("ArticleId").notNull(),
    createdAt: timestamp("CreatedAt").defaultNow().notNull(),
    updatedAt: timestamp("UpdatedAt").defaultNow().notNull().$onUpdate(getCurrentDate),
  },
  (table) => [
    primaryKey({ columns: [table.userId, table.articleId] }),
    index("ArticleRead_UserId_FK_Index").on(table.userId),
    index("ArticleRead_ArticleId_Index").on(table.articleId),
  ]
).enableRLS();

export const articlesReadsRelations = relations(articlesReads, ({ one }) => ({
  user: one(users, {
    fields: [articlesReads.userId],
    references: [users.id],
  }),
}));

export type ArticleReadInsert = InferInsertModel<typeof articlesReads>;
export type ArticleRead = InferSelectModel<typeof articlesReads>;
export type ArticleReadSql = InferPgSelectModel<typeof articlesReads>;
