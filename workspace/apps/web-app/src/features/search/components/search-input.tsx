import { useSearchPageStore } from "~/stores/searchPage.store.ts";

import { type FunctionComponent } from "react";

export const SearchInput: FunctionComponent = () =>
{
  const setQuery = useSearchPageStore((s) => s.setQuery);
  const query = useSearchPageStore((s) => s.query);

  return (
    <input
      autoFocus={true}
      className="block w-full border-b border-muted-5 bg-transparent py-8 pl-7 pr-2 font-serif text-2xl"
      onChange={(event) => setQuery(event.target.value)}
      placeholder="Suchen"
      type="search"
      value={query}
    />
  );
};
