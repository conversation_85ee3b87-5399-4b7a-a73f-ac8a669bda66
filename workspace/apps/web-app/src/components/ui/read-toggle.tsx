import useArticleHasRead from "~/hooks/useArticleHasRead.ts";
import { useToggleArticleHasRead } from "~/hooks/useToggleArticleHasRead.ts";
import { cn } from "~/lib/utils.ts";

import { BookCheckIcon, BookOpenIcon } from "lucide-react";
import { type FC } from "react";

import { Button } from "./button.tsx";

interface ReadToggleProps
{
  readonly articleId: string;
  readonly className?: string;
}

export const ReadToggle: FC<ReadToggleProps> = ({ articleId, className }) =>
{
  const { hasRead = false, isLoading } = useArticleHasRead(articleId);
  const { isPending, mutate: toggleReadStatus } = useToggleArticleHasRead();

  const handleToggle = () =>
  {
    toggleReadStatus(articleId);
  };

  return (
    <Button
      type="button"
      onClick={handleToggle}
      disabled={isLoading || isPending}
      rounded={false}
      variant={hasRead ? "secondary" : "defaultDarkRed"}
      size="md"
      className={cn(
        !hasRead && "!text-white",
        "border-none shadow-lg rounded-xl font-thin flex items-center gap-4",
        className
      )}
      data-read-toggle="true"
      aria-label={hasRead ? "Als ungelesen markieren" : "Als gelesen markieren"}>
      {hasRead ? (
        <>
          <BookCheckIcon size={18} />
          <span>Als ungelesen markieren</span>
        </>
      ) : (
        <>
          <BookOpenIcon size={18} />
          <span>Als gelesen markieren</span>
        </>
      )}
    </Button>
  );
};
