import { eq } from "@constellatio/db";
import { type ProfilePictureInsert, profilePictures, users } from "@constellatio/db/schema";
import { updateUserDetailsSchema } from "@constellatio/schemas/routers/auth/updateUserDetails.schema";
import { generateCreateSignedUploadUrlSchema } from "@constellatio/schemas/routers/uploads/createSignedUploadUrl.schema";
import { setOnboardingResultSchema } from "@constellatio/schemas/routers/users/setOnboardingResult.schema";
import { setProfilePictureSchema } from "@constellatio/schemas/routers/users/setProfilePicture.schema";
import { imageFileExtensions, imageFileMimeTypes } from "@constellatio/shared/validation";

import { getUserWithRelations } from "./users.service.js";
import { addUserToCrmUpdateQueue } from "../../../lib/clickup/utils.js";
import {
  type LoopsContactProperties,
  updateLoopsContactEmail,
  updateLoopsContactProperties,
} from "../../../lib/loops/loops.js";
import { stripe } from "../../../lib/stripe/stripe.js";
import { filterUserForClient } from "../../../utils/filters.js";
import { NotFoundError, UnauthorizedError } from "../../../utils/serverError.js";
import { createTRPCRouter, protectedProcedure } from "../../trpc.js";
import { getSignedCloudStorageUploadUrl } from "../uploads/uploads.services.js";

export const usersRouter = createTRPCRouter({
  createSignedProfilePictureUploadUrl: protectedProcedure
    .input(generateCreateSignedUploadUrlSchema(imageFileExtensions, imageFileMimeTypes))
    .mutation(async ({ ctx: { userId }, input: file }) =>
    {
      return getSignedCloudStorageUploadUrl({
        bucketType: "public",
        file,
        userId,
      });
    }),
  getOnboardingResult: protectedProcedure.query(async ({ ctx: { db, userId } }) =>
  {
    const onboardingResult = await db
      .select({ onboardingResult: users.onboardingResult })
      .from(users)
      .where(eq(users.id, userId));

    return onboardingResult[0]?.onboardingResult ?? null;
  }),
  getUserDetails: protectedProcedure.query(async ({ ctx: { db, userId } }) =>
  {
    try
    {
      const user = await getUserWithRelations(db, userId);
      return filterUserForClient(user);
    }
    catch (e: unknown)
    {
      if (e instanceof NotFoundError)
      {
        console.log("User not found - unauthorized");
        throw new UnauthorizedError();
      }
      throw e;
    }
  }),
  markLearningPathsExplanationAsSeen: protectedProcedure.mutation(async ({ ctx: { db, userId } }) =>
  {
    await db.update(users).set({ hasSeenLearningPathsExplanation: true }).where(eq(users.id, userId));
  }),
  setOnboardingResult: protectedProcedure
    .input(setOnboardingResultSchema)
    .mutation(async ({ ctx: { db, userId }, input: { result } }) =>
    {
      const fullUser = await db.update(users).set({ onboardingResult: result }).where(eq(users.id, userId)).returning();
      if (fullUser[0])
      {
        await updateLoopsContactProperties(fullUser[0].email, {
          onboardingResult: result,
        });
      }
      else
      {
        console.error("Loops onboarding result update failed");
      }
    }),
  setProfilePicture: protectedProcedure
    .input(setProfilePictureSchema)
    .mutation(async ({ ctx: { db, userId }, input: file }) =>
    {
      const profilePictureInsert: ProfilePictureInsert = {
        contentType: file.contentType,
        fileExtension: file.fileExtensionLowercase,
        id: file.id,
        profilePictureSource: "internal",
        serverFilename: file.serverFilename,
        userId,
      };

      await db
        .insert(profilePictures)
        .values(profilePictureInsert)
        .onConflictDoUpdate({
          set: {
            contentType: profilePictureInsert.contentType,
            fileExtension: profilePictureInsert.fileExtension,
            id: profilePictureInsert.id,
            profilePictureSource: "internal",
            serverFilename: profilePictureInsert.serverFilename,
          },
          target: profilePictures.userId,
        });
    }),
  updateUserDetails: protectedProcedure
    .input(updateUserDetailsSchema)
    .mutation(async ({ ctx: { db, userId }, input }) =>
    {
      const [updatedUser] = await db.update(users).set(input).where(eq(users.id, userId)).returning();

      if (input.email != null && updatedUser?.stripeCustomerId != null)
      {
        await stripe.customers.update(updatedUser.stripeCustomerId, {
          email: input.email,
        });
      }

      if (input.email != null)
      {
        await updateLoopsContactEmail(input.email, userId);
      }

      if (updatedUser != null)
      {
        const userProperties: LoopsContactProperties = {
          constellatioSubscriptionStatus: updatedUser.subscriptionStatus ?? null,
          firstName: updatedUser.firstName ?? null,
          gender: updatedUser.gender ?? null,
          lastName: updatedUser.lastName ?? null,
          onboardingResult: updatedUser.onboardingResult ?? null,
          semester: updatedUser.semester ?? null,
          university: updatedUser.university ?? null,
        };

        await updateLoopsContactProperties(updatedUser.email, userProperties);
      }

      await addUserToCrmUpdateQueue(db, userId);
    }),
});
