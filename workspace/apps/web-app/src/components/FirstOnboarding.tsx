/* eslint-disable max-lines */
import { But<PERSON> } from "~/components/ui/button.tsx";
import { Form, FormControl, FormField, FormItem, FormMessage } from "~/components/ui/form.tsx";
import { Input } from "~/components/ui/input.tsx";
import { Progress } from "~/components/ui/progress.tsx";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select.tsx";
import useContextAndErrorIfNull from "~/hooks/useContextAndErrorIfNull.ts";
import useSetOnboardingSurvey from "~/hooks/useSetOnboardingSurvey.ts";
import useSubscription from "~/hooks/useSubscription.ts";
import useUserDetails from "~/hooks/useUserDetails.ts";
import { cn } from "~/lib/utils.ts";
import { InvalidateQueriesContext } from "~/providers/InvalidateQueriesProvider.tsx";
import { api, queryClient } from "~/utils/trpc.ts";

import { allUniversities } from "@constellatio/schemas/common/auth/userData.validation";
import {
  type OnboardingSurveySchema,
  onboardingSurveySchema,
} from "@constellatio/schemas/routers/users/setOnboardingSurvey.schema";
import { appPaths } from "@constellatio/shared/paths";
import { zodResolver } from "@hookform/resolvers/zod";
import { PopoverClose } from "@radix-ui/react-popover";
import { useMutation } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { Check, ChevronDown } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { CalendlyIntegration } from "./CalendlyIntegration.tsx";
import { ConstellatioLogo } from "./ConstellatioLogo.tsx";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogContent,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "./ui/alert-dialog.tsx";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "./ui/command.tsx";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover.tsx";
import { Separator } from "./ui/separator.tsx";

// Definiere die Schritte
type Step = {
  description?: React.ReactNode;
  title: string;
};

function isFieldOptional<T extends z.ZodRawShape>(schema: z.ZodObject<T>, key: keyof T): boolean
{
  const field = schema.shape[key];
  return field?.isOptional?.() || field instanceof z.ZodOptional;
}

const baseSteps: Step[] = [
  {
    title: "Wie ist dein Status?",
  },
  {
    title: "Deine Universität",
  },
  {
    description: "Wähle alle Rechtsgebiete aus, die dich interessieren",
    title: "Welche Rechtsgebiete interessieren dich?",
  },
  {
    description: "Wähle eine Quelle aus",
    title: "Wie bist du auf Constellatio aufmerksam geworden?",
  },
  {
    description: "Du kannst Auswahl später noch anpassen",
    title: "Wie möchtest du durchstarten?",
  },
];

const bookingStep: Step = {
  description: (
    <>
      Mit Constellatio wird juristisches Lernen endlich zeitgemäß.
      <br />
      Das Wichtigste für uns: Euer Lernerfolg. Weil wir immer wieder sehen, dass echtes digitales Lernen für viele eine
      Umstellung ist, nimmt sich einer der Constellatio-Gründer persönlich für dich Zeit, um dir in 10 min unser
      einzigartiges Lernkonzept zu zeigen. So sparst du dir die Zeit, dich selbst einzuarbeitem und erlebst noch
      schneller Lernerfolge. <br />
      Die einzigartige Didaktik von Constellatio ist der Gamechanger. Herzlich willkommen!
    </>
  ),
  title: "Bonus-Angebot: +10 Tage gratis!",
};

const studentTypes = ["Examenskandidat", "Jurastudent", "Referendar", "Doktorand", "Berufsanfänger", "Andere"] as const;

const universityNames = allUniversities.map((uni) => uni.name);

const universities = ["Andere Universität", ...universityNames] as const;

const lawAreas = [
  {
    areaName: "Zivilrecht",
    options: ["BGB AT", "SchuldR", "SachenR", "FamilienR", "ErbR", "GesellschaftsR"],
  },
  {
    areaName: "Öffentliches Recht",
    options: [
      "Grundrechte",
      "StaatsorganisationsR",
      "StaatshaftungsR",
      "BauR",
      "PolizeiR",
      "KommunalR",
      "VerwR AT",
      "VerwProzR",
      "EuropaR",
    ],
  },
  {
    areaName: "Strafrecht",
    options: ["StrafR AT", "StrafR BT"],
  },
] as const;

const referralSources = [
  "Freunde",
  "Google",
  "Instagram",
  "LinkedIn",
  "YouTube",
  "TikTok",
  "Reddit",
  "Andere",
] as const;

// Verwende das Schema direkt ohne Erweiterungen
type FormSchema = OnboardingSurveySchema;

export default function FirstOnboarding()
{
  const navigate = useNavigate();
  const { refetch: refetchSubscriptionDetails } = useSubscription();
  const { setOnboardingSurvey } = useSetOnboardingSurvey();
  const [currentStep, setCurrentStep] = useState(0);
  const { userDetails } = useUserDetails();
  const [showCalendar, setShowCalendar] = useState(false);
  const [howToContinue, setHowToContinue] = useState<"freeContent" | "subscribtionContent" | "promoCode" | undefined>(
    undefined
  );
  const steps = howToContinue === "subscribtionContent" ? [...baseSteps, bookingStep] : baseSteps;
  const progress = ((currentStep + 1) / steps.length) * 100;

  const [promoCodeInput, setPromoCodeInput] = useState("");
  const [promoCodeMessage, setPromoCodeMessage] = useState<{ text: string; type: "error" | "success" } | null>(null);
  const [showTrailSuccessDialog, setShowTrailSuccessDialog] = useState(false);

  const selfServiceTrialMutation = useMutation(api.billing.selfServiceTrial.mutationOptions());
  const { invalidateSubscriptionDetails } = useContextAndErrorIfNull(InvalidateQueriesContext);

  const form = useForm<FormSchema>({
    defaultValues: {
      howToContinue: undefined,
      lawAreas: [],
      lawAreasOther: "",
      referralSource: "",
      studentType: "",
      university: "",
    },
    mode: "onChange",
    resolver: zodResolver(onboardingSurveySchema),
  });

  const { getValues, setValue, trigger, watch } = form;
  const formValues = watch();

  useEffect(() =>
  {}, [formValues]);

  const toggleLawArea = (area: string) =>
  {
    const currentAreas = form.getValues("lawAreas") || [];
    if (currentAreas.includes(area))
    {
      setValue(
        "lawAreas",
        currentAreas.filter((a) => a !== area),
        { shouldDirty: true, shouldValidate: true }
      );
    }
    else
    {
      setValue("lawAreas", [...currentAreas, area], { shouldDirty: true, shouldValidate: true });
    }
  };

  const handleSubmit = async (formValues: FormSchema) =>
  {
    const { howToContinue, lawAreas, lawAreasOther, referralSource, studentType, university } = formValues;

    const surveyData: OnboardingSurveySchema = {
      howToContinue,
      lawAreas: Array.isArray(lawAreas) ? lawAreas : [],
      lawAreasOther,
      referralSource,
      studentType,
      university,
    };

    await setOnboardingSurvey(surveyData);

    queryClient.removeQueries({ queryKey: api.surveys.getIsOnboardingCompleted.queryKey() });

    if (currentStep === steps.length - 1 && howToContinue === "subscribtionContent" && !showCalendar)
    {
      await navigate({ to: "/dashboard" });
    }
    else if (howToContinue === "freeContent")
    {
      await navigate({ to: "/flashcards" });
    }
    else if (howToContinue === "subscribtionContent" && currentStep < steps.length - 1)
    {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const { isPending: isRedeemingPromoCode, mutate: redeemPromoCodeMutate } = useMutation(
    api.billing.redeemPromoCode.mutationOptions({
      onError: (e) =>
      {
        const errorMsg = e.data?.clientError?.details?.message ?? "Ungültiger oder abgelaufener Code.";
        setPromoCodeMessage({ text: errorMsg, type: "error" });
      },
      onSuccess: async () =>
      {
        setPromoCodeMessage({ text: "Gutscheincode erfolgreich eingelöst!", type: "success" });
        form.setValue("howToContinue", "promoCode", { shouldDirty: true, shouldValidate: true });
        setHowToContinue("promoCode");
        setPromoCodeInput("");

        await handleSubmit(getValues());

        await refetchSubscriptionDetails();
        queryClient.removeQueries({ queryKey: api.surveys.getIsOnboardingCompleted.queryKey() });
        setShowTrailSuccessDialog(true);
      },
    })
  );

  const handleRedeemPromoCode = () =>
  {
    if (!promoCodeInput.trim())
    {
      setPromoCodeMessage({ text: "Bitte gib einen Code ein.", type: "error" });
      return;
    }
    setPromoCodeMessage(null);
    redeemPromoCodeMutate({ code: promoCodeInput.trim() });
  };

  const isCurrentStepValid = async () =>
  {
    switch (currentStep)
    {
      case 0:
        return trigger("studentType");
      case 1:
        return trigger("university");
      case 2:
        return trigger("lawAreas");
      case 3:
        return trigger("referralSource");
      case 4:
        return formValues.howToContinue !== undefined;
      case 5:
        // is always true because this step only exists to give the user the option to talk to sven
        return true;
      default:
        return false;
    }
  };

  const handleNext = async () =>
  {
    const isValid = await isCurrentStepValid();

    if (!isValid) return;

    // if current step is 0 and value is "Andere"
    if (currentStep === 0 && getValues("studentType") === "Andere")
    {
      setValue("university", "nicht erhoben");
      setCurrentStep((prev) => prev + 2);
    }
    else if (currentStep === 4)
    {
      if (howToContinue === "subscribtionContent")
      {
        await selfServiceTrialMutation.mutateAsync();
        await invalidateSubscriptionDetails();
      }
      await handleSubmit(getValues());
    }
    else if (currentStep === 5)
    {
      await handleSubmit(getValues());
    }
    else if (currentStep < steps.length - 1 && howToContinue !== "freeContent")
    {
      setCurrentStep((prev) => prev + 1);
    }
    else
    {
      await handleSubmit(getValues());
    }
  };

  const handlePrev = () =>
  {
    if (currentStep > 0)
    {
      if (currentStep === 2 && getValues("studentType") === "Andere")
      {
        setValue("university", "");
        setCurrentStep((prev) => prev - 2);
      }
      else setCurrentStep((prev) => prev - 1);
    }
  };

  const isNextButtonDisabled = () =>
  {
    switch (currentStep)
    {
      case 0:
        return !getValues("studentType");
      case 1:
        return !getValues("university");
      case 2:
        // this always returns false due to the extra free text field
        // slight workaround so that I don't have to implement a custom validator
        return getValues("lawAreas")?.length === 0 && getValues("lawAreasOther") === "";
      case 3:
        return !getValues("referralSource");
      case 4:
        return !getValues("howToContinue");
      default:
        return false;
    }
  };

  // Skip-Funktion für optionale Felder
  const handleSkip = () =>
  {
    // TODO: clean current step data
    switch (currentStep)
    {
      case 0:
        setValue("studentType", "");
        break;
      case 1:
        setValue("university", "");
        break;
      default:
        break;
    }
    setCurrentStep((prev) => prev + 1);
  };

  if (showCalendar && userDetails)
  {
    return <CalendlyIntegration {...userDetails} />;
  }

  return (
    <>
      <AlertDialog open={showTrailSuccessDialog} onOpenChange={setShowTrailSuccessDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Test-Zeitraum erfolgreich aktiviert</AlertDialogTitle>
            {/* <AlertDialogDescription>
              Lege jetzt los und bereite dich mit unseren Lernpfaden auf die Übung für Fortgeschrittene im Zivilrecht
              vor. Nutze unser Karteikarten-Feature mit Spaced Repetition, um dein Wissen effizient zu wiederholen.
            </AlertDialogDescription> */}
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogAction onClick={async () => navigate({ to: appPaths.dashboard })}>
              Weiter zum Dashboard
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <div className="mx-auto max-w-[750px] px-6 bg-muted border border-input w-full h-fit rounded-md overflow-y-auto max-h-[90vh]">
        <div className="relative">
          <div className="pb-6 pt-6">
            <div className="space-y-6">
              <ConstellatioLogo withTitle={true} />
              <Progress value={progress} className="h-2" />
              <h1 className="text-2xl font-medium">{steps[currentStep]?.title}</h1>
              {steps[currentStep]?.description && (
                <p className="text-base text-muted-foreground">{steps[currentStep].description}</p>
              )}
            </div>

            <Form {...form}>
              <form
                className="flex min-h-[200px] flex-col justify-between rounded-md mt-6"
                onSubmit={(e) =>
                {
                  e.preventDefault();
                  void handleNext();
                }}>
                {/* Alle Schritte werden direkt eingebunden und mit CSS conditionally gerendert */}

                {/* Schritt 0: Studententyp */}
                <div className={currentStep === 0 ? "block" : "hidden"}>
                  <FormField
                    control={form.control}
                    name="studentType"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="h-12">
                              <SelectValue className="text-foreground" placeholder="Wähle deinen Stadium aus" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {studentTypes.map((type) => (
                              <SelectItem key={type} value={type}>
                                {type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Schritt 1: Universität */}
                <div className={currentStep === 1 ? "block" : "hidden"}>
                  <FormField
                    control={form.control}
                    name="university"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant="secondary"
                                role="combobox"
                                className={cn(
                                  "w-full justify-between rounded-md border-none h-12 text-sm",
                                  !field.value && "text-muted-foreground"
                                )}>
                                {field.value ? universities.find((u) => u === field.value) : "Universität auswählen"}
                                <ChevronDown className="opacity-50" />
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height] p-0">
                            <Command>
                              <CommandInput placeholder="Universitäten durchsuchen..." className="h-9" />
                              <CommandList>
                                <CommandEmpty>Keine Universität gefunden.</CommandEmpty>
                                <CommandGroup>
                                  {universities.map((u) => (
                                    <CommandItem
                                      value={u}
                                      key={u}
                                      className="hover:bg-muted-11 hover:text-muted-0 py-0"
                                      onSelect={() =>
                                      {
                                        form.setValue("university", u);
                                      }}>
                                      <PopoverClose className="flex w-full gap-2 py-2">
                                        <span className="flex size-3.5 items-center justify-center">
                                          {u === field.value && <Check className="size-4" />}
                                        </span>
                                        {u}
                                      </PopoverClose>
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Schritt 2: Rechtsgebiete */}
                <div className={currentStep === 2 ? "block" : "hidden"}>
                  <div className="flex flex-col gap-6">
                    <FormField
                      control={form.control}
                      name="lawAreas"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <div className="flex flex-col gap-6">
                            {lawAreas.map((area) =>
                            {
                              return (
                                <div key={area.areaName} className="flex flex-col gap-2">
                                  <h3 className="text-xl font-bold w-full text-center">{area.areaName}</h3>
                                  <div key={area.areaName} className="flex flex-row gap-2 flex-wrap justify-center">
                                    {area.options.map((option) => (
                                      <Button
                                        key={option}
                                        type="button"
                                        variant={field.value?.includes(option) ? "default" : "secondary"}
                                        className="h-12 px-6 text-base font-normal"
                                        onClick={() => toggleLawArea(option)}>
                                        {option}
                                      </Button>
                                    ))}
                                  </div>
                                </div>
                              );
                            })}
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <h3 className="text-xl font-bold w-full text-center">Nicht aufgelistet?</h3>
                    <FormField
                      control={form.control}
                      name="lawAreasOther"
                      render={({ field }) => (
                        <FormItem className="w-full">
                          <Input
                            placeholder="Schreib uns was dir fehlt"
                            value={field.value}
                            onChange={field.onChange}
                            className="h-12 px-6 text-base font-normal"
                          />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Schritt 3: Referral */}
                <div className={currentStep === 3 ? "block" : "hidden"}>
                  <FormField
                    control={form.control}
                    name="referralSource"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <div className="flex justify-center">
                          <div className="flex max-w-[600px] flex-wrap justify-center gap-3">
                            {referralSources.map((source) => (
                              <Button
                                key={source}
                                type="button"
                                variant={field.value === source ? "default" : "secondary"}
                                className="h-12 px-6 text-base font-normal"
                                onClick={() =>
                                {
                                  field.onChange(source);
                                }}>
                                {source}
                              </Button>
                            ))}
                          </div>
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Schritt 4: Optionen */}
                <div className={currentStep === 4 ? "block" : "hidden"}>
                  <FormField
                    control={form.control}
                    name="howToContinue"
                    render={({ field }) => (
                      <>
                        <FormItem className="w-full">
                          <div className="space-y-3">
                            {/* Subscription Content Option */}
                            <div
                              className={`group flex w-full flex-col rounded-xl border transition-colors duration-200 ${
                                field.value === "subscribtionContent"
                                  ? "border-primary bg-muted-3 text-foreground"
                                  : "border-border bg-muted-0"
                              }`}>
                              <button
                                type="button"
                                className="flex min-w-0 flex-1 flex-col items-start p-4 text-left hover:bg-muted/90 w-full"
                                onClick={() =>
                                {
                                  field.onChange("subscribtionContent");
                                  setHowToContinue("subscribtionContent");
                                  setPromoCodeMessage(null);
                                }}>
                                <h3 className="font-sans font-medium">
                                  Sofort loslegen: 5 Tage fallbasiertes Lernen testen
                                </h3>
                                <p className="whitespace-normal text-sm text-muted-foreground/90">
                                  Lernpfade führen dich wie ein roter Faden durch interaktive Fälle und das Lexikon.
                                  Dazu spezielle juristische Karteikarten und viel mehr. Kein Abo!
                                </p>
                              </button>
                            </div>
                          </div>

                          {/* Free Content Option */}
                          {/* <button
                            className={`group flex w-full flex-row items-center rounded-xl border transition-colors duration-200 mt-3 ${
                              field.value === "freeContent"
                                ? "border-primary bg-muted text-foreground hover:bg-muted/90"
                                : "border-border bg-white hover:bg-muted"
                            }`}
                            onClick={() =>
                            {
                              field.onChange("freeContent");
                              setHowToContinue("freeContent");
                              setPromoCodeMessage(null);
                            }}
                            type="button">
                            <div className="flex min-w-0 flex-1 flex-col items-start p-4">
                              <h3 className="font-sans font-medium text-left">
                                Nur die Karteikarten nutzen (für immer kostenlos)
                              </h3>
                              <p className="whitespace-normal text-sm text-muted-foreground/90 text-left">
                                Karteikarten, speziell entwickelt für Juristen - mit einem Algorithmus, der dich die
                                Inhalte immer genau zum richtigen Zeitpunkt wiederholen lässt.
                              </p>
                            </div>
                          </button> */}
                          <FormMessage />
                        </FormItem>
                        <div className="py-4 space-y-2">
                          <p className="text-base text-muted-foreground">Oder Gutscheincode einlösen:</p>
                          <div className="flex items-start gap-2">
                            <Input
                              type="text"
                              placeholder="Gutscheincode"
                              value={promoCodeInput}
                              onChange={(e) =>
                              {
                                setPromoCodeInput(e.target.value);
                                if (promoCodeMessage) setPromoCodeMessage(null);
                              }}
                              className="h-10 flex-grow"
                            />
                            <Button
                              type="button"
                              onClick={handleRedeemPromoCode}
                              disabled={isRedeemingPromoCode || !promoCodeInput.trim()}
                              className="h-10 shrink-0">
                              {isRedeemingPromoCode ? "Prüfe..." : "Anwenden"}
                            </Button>
                          </div>
                          {promoCodeMessage && (
                            <p
                              className={`text-xs ${
                                promoCodeMessage.type === "error" ? "text-destructive" : "text-green-600"
                              }`}>
                              {promoCodeMessage.text}
                            </p>
                          )}
                        </div>
                      </>
                    )}
                  />
                </div>

                {/* Schritt 5: Buchung */}
                <div className={currentStep === 5 && howToContinue === "subscribtionContent" ? "block" : "hidden"}>
                  <div className="bg-muted rounded-md p-6">
                    <div className="flex flex-col items-center justify-center space-y-4 w-full">
                      <Button
                        type="button"
                        onClick={() =>
                        {
                          setShowCalendar(true);
                        }}
                        className="h-12 px-8 text-base font-medium">
                        Pers. Onboarding (+10 Tage)
                      </Button>
                      <Button
                        type="button"
                        variant="secondary"
                        onClick={async () =>
                        {
                          await handleNext();
                        }}
                        className="h-12 px-8">
                        Sofort selbst loslegen
                      </Button>
                    </div>
                  </div>
                </div>

                {currentStep !== 5 && (
                  <>
                    <Separator className="my-4" />
                    <div className="flex justify-between">
                      {currentStep > 0 ? (
                        <Button type="button" variant="secondary" onClick={handlePrev} className="h-12 px-8">
                          Zurück
                        </Button>
                      ) : (
                        <div />
                      )}

                      <div className="flex gap-2">
                        {isFieldOptional(
                          onboardingSurveySchema,
                          currentStep === 0
                            ? "studentType"
                            : currentStep === 1
                              ? "university"
                              : currentStep === 3
                                ? "referralSource"
                                : "howToContinue"
                        ) && (
                          <Button type="button" variant="ghost" onClick={handleSkip} className="h-12 px-8">
                            Überspringen
                          </Button>
                        )}
                        <Button type="submit" className="h-12 px-8" disabled={isNextButtonDisabled()}>
                          {currentStep < steps.length - 1 ? "Weiter" : "Fertig"}
                        </Button>
                      </div>
                    </div>
                  </>
                )}
              </form>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
}

/* korrekte url für onboarding calender termin mit sven
zimmes/constellatio-onboarding
 */
