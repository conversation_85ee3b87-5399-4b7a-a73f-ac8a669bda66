/* eslint-disable max-lines */
/* FIXME: fix button prefetch error */
import ContentWrapper from "~/components/helpers/contentWrapper/ContentWrapper.tsx";
import { OverlayLines } from "~/components/Icons/bg-layer.tsx";
import { Bookmark } from "~/components/Icons/Bookmark.tsx";
import { BookmarkFilledIcon } from "~/components/Icons/BookmarkFilledIcon.tsx";
import IconButtonBar from "~/components/organisms/iconButtonBar/IconButtonBar.tsx";
import { mainCategoryKeySchema } from "~/components/pages/library/utils/filterContent.ts";
import useAddBookmark from "~/hooks/useAddBookmark.ts";
import useArticles from "~/hooks/useArticles.ts";
import useBookmarks from "~/hooks/useBookmarks.ts";
import useCases from "~/hooks/useCases.ts";
import useRemoveBookmark from "~/hooks/useRemoveBookmark.ts";
import { getUrlSearchParams } from "~/utils/helpers.ts";

import {
  type IGenLegalArea,
  type IGenMainCategory,
  type IGenTopic,
  type IGenArticle,
  type Maybe,
} from "@constellatio/cms/app/generated-types";
import { type AddOrRemoveBookmarkSchema } from "@constellatio/schemas/routers/bookmarks/addOrRemoveBookmark.schema";
import { appPaths } from "@constellatio/shared/paths";
import { queryParams } from "@constellatio/shared/query-params";
import { type Nullable } from "@constellatio/utility-types";
import { Button, Title } from "@mantine/core";
import { IconArrowLeft, IconArrowRight } from "@tabler/icons-react";
import { Link } from "@tanstack/react-router";
import { type FunctionComponent, useEffect, useState } from "react";

import * as styles from "./CaseSolvingHeader.styles.ts";
import OverviewCard, { type IOverviewCard } from "../overviewCard/OverviewCard.tsx";

export interface ICaseSolvingHeaderProps
{
  readonly caseId?: Maybe<string> | undefined;
  readonly legalArea: Nullable<IGenLegalArea>;
  readonly mainCategory: Nullable<IGenMainCategory>;
  readonly nextArticleId: Nullable<string>;
  readonly overviewCard: IOverviewCard;
  readonly previousArticleId: Nullable<string>;
  readonly title: string;
  readonly topic: Nullable<IGenTopic>;
  readonly variant: "case" | "dictionary";
}

const CaseSolvingHeader: FunctionComponent<ICaseSolvingHeaderProps> = ({
  caseId,
  legalArea,
  mainCategory,
  nextArticleId,
  overviewCard,
  previousArticleId,
  title,
  topic,
  variant,
}) =>
{
  const { allCases = [] } = useCases();
  const { allArticles = [] } = useArticles();
  const { bookmarks } = useBookmarks(undefined);
  const allCasesBookmarks = bookmarks.filter((bookmark) => bookmark?.resourceType === "case") ?? [];
  const allArticlesBookmarks = bookmarks.filter((bookmark) => bookmark?.resourceType === "article") ?? [];
  const bookmarkedArticles = allArticles.filter((caisyArticle: IGenArticle) =>
    allArticlesBookmarks.some((bookmark) => bookmark.resourceId === caisyArticle.id)
  );
  const bookmarkedCases = allCases.filter((caisyCase) =>
    allCasesBookmarks.some((bookmark) => bookmark.resourceId === caisyCase.id)
  );
  const isItemBookmarked =
    bookmarkedCases.some((bookmark) => bookmark.title === title) ||
    bookmarkedArticles?.some((bookmark) => bookmark.title === title) ||
    false;
  const { mutate: addBookmark } = useAddBookmark();
  const { mutate: removeBookmark } = useRemoveBookmark({ shouldUseOptimisticUpdate: true });
  const [referringLearningPath, setReferringLearningPath] = useState<Nullable<string>>();

  useEffect(() =>
  {
    const searchParams = new URLSearchParams(getUrlSearchParams());
    const _referringLearningPathParam = searchParams.get(queryParams.referringLearningPath);

    if (!_referringLearningPathParam)
    {
      return;
    }

    setReferringLearningPath(
      `${_referringLearningPathParam}#${searchParams.get(queryParams.referringLearningPathUnit)}`
    );
  }, []);

  const onBookmarkIconClick = (): void =>
  {
    if (!caseId)
    {
      return;
    }

    const bookmarkData: AddOrRemoveBookmarkSchema = {
      resourceId: caseId,
      resourceType: variant === "case" ? "case" : "article",
    };

    if (!isItemBookmarked)
    {
      addBookmark(bookmarkData);
      return;
    }
    else
    {
      removeBookmark(bookmarkData);
    }
  };

  const icons = [
    {
      click: () => onBookmarkIconClick(),
      src: isItemBookmarked ? <BookmarkFilledIcon /> : <Bookmark />,
      title: "Bookmark",
    },
    // { src: <Pin/>, title: "Pin" },
  ];

  const parsedMainCategory = mainCategoryKeySchema.parse(mainCategory?.mainCategory);

  return (
    <div css={styles.wrapper({ variant })}>
      <ContentWrapper>
        <div id="overlay-lines">
          <OverlayLines />
          {/* <OverlayLines2 width="1440" height="488" scale={2} transform="translate(400, 0)" overflow={"hidden"} /> */}
        </div>
        <div css={[styles.body, variant === "dictionary" && styles.bodyArticles]}>
          <div css={styles.bodyText}>
            <div className="icons-bar">
              {referringLearningPath && (
                <Button
                  type="button"
                  // prefetch={true}
                  component={Link}
                  href={`${appPaths.learningPaths}/${referringLearningPath}`}
                  css={[styles.navButton, styles.backToLearningPathButton]}
                  leftIcon={<IconArrowLeft />}
                  variant="outline">
                  Zurück zum Lernpfad
                </Button>
              )}
              <IconButtonBar icons={icons} />
            </div>
            <div className="bread-crumb">
              {parsedMainCategory && legalArea && topic && (
                <>
                  <Link to={"/library"} search={{ category: parsedMainCategory }}>
                    {parsedMainCategory}
                  </Link>
                  {" / "}
                  <Link
                    to={"/library"}
                    search={{ category: parsedMainCategory, legalAreaId: legalArea.id! }}
                    hash={legalArea.id!}>
                    {legalArea.legalAreaName}
                  </Link>
                  {" / "}
                  <Link
                    to={"/library"}
                    search={{
                      category: parsedMainCategory,
                      legalAreaId: legalArea.id!,
                      topicId: topic.id!,
                    }}
                    hash={topic.id!}>
                    {topic.topicName}
                  </Link>
                </>
              )}
            </div>
            <Title title={title} order={1}>
              {title}
            </Title>
          </div>
          <div css={styles.bodyCard}>
            <OverviewCard {...overviewCard} />
          </div>
        </div>
      </ContentWrapper>
    </div>
  );
};

export default CaseSolvingHeader;
