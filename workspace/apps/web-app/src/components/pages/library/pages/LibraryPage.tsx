/* eslint-disable max-lines */

import { BodyText } from "~/components/atoms/BodyText/BodyText.tsx";
import { Button } from "~/components/atoms/Button/Button.tsx";
import ContentWrapper from "~/components/helpers/contentWrapper/ContentWrapper.tsx";
import { FiltersList } from "~/components/Icons/FiltersList.tsx";
import { Trash } from "~/components/Icons/Trash.tsx";
import FilterTag from "~/components/molecules/filterTag/FilterTag.tsx";
import OverviewHeader from "~/components/organisms/OverviewHeader/OverviewHeader.tsx";
import { LibraryOverviewFiltersDrawer } from "~/components/pages/library/components/filtersDrawer/LibraryFiltersDrawer.tsx";
import { getArticlesWithReadStatus, getCasesWithProgress } from "~/components/pages/library/utils/contentProgress.ts";
import { getContentMatchingTheFilters, mainCategoryKeySchema } from "~/components/pages/library/utils/filterContent.ts";
import { getGroupedContentForMainCategory } from "~/components/pages/library/utils/groupedContent.ts";
import { getCountedMainCategories } from "~/components/pages/library/utils/mainCategories.ts";
import useCasesProgress from "~/hooks/useCasesProgress.ts";
import { useReadArticles } from "~/hooks/useReadArticles.ts";
import { useSeenArticles } from "~/hooks/useSeenArticles.ts";
import { useLibraryFiltersStore } from "~/stores/libraryFilters.store.ts";
import { api } from "~/utils/trpc.ts";

import { mapToObject, objectKeys } from "@constellatio/utils/object";
import { Title } from "@mantine/core";
import { useSuspenseQuery } from "@tanstack/react-query";
import { useNavigate, useSearch } from "@tanstack/react-router";
import { Fragment, useMemo } from "react";
import { useStoreWithEqualityFn } from "zustand/traditional";

import * as styles from "./LibraryPage.styles.ts";
import { ContentTypeFilterBlock } from "../components/contentTypefilterBlock/contentTypefilterBlock.tsx";
import { DeleteButton } from "../components/DeleteButton/DeleteButton.tsx";
import { LegalAreaBlock } from "../components/legalAreaBlock/LegalAreaBlock.tsx";

export const LibraryPage = () =>
{
  const { data: casesProgress } = useCasesProgress();
  const { data: readArticles } = useReadArticles();
  const { data: seenArticles } = useSeenArticles();
  const { data: allArticles } = useSuspenseQuery(api.caisy.getAllArticles.queryOptions());
  const { data: allCases } = useSuspenseQuery(api.caisy.getAllCases.queryOptions());
  const { category } = useSearch({ from: "/_authed/_onboarded/_subscribed/_legacy/library/" });
  const navigate = useNavigate();
  const filters = useStoreWithEqualityFn(useLibraryFiltersStore, (s) => s.filters);
  const openDrawer = useStoreWithEqualityFn(useLibraryFiltersStore, (s) => s.openDrawer);
  const clearAllFilters = useStoreWithEqualityFn(useLibraryFiltersStore, (s) => s.clearAllFilters);
  const totalFiltersCount = useStoreWithEqualityFn(useLibraryFiltersStore, (s) => s.getTotalFiltersCount());

  const selectedContentType = useStoreWithEqualityFn(useLibraryFiltersStore, (s) => s.getSelectedContentType());
  const setSelectedContentType = useStoreWithEqualityFn(useLibraryFiltersStore, (s) => s.setSelectedContentType);

  const handleClearAllFilters = () =>
  {
    clearAllFilters();
    setSelectedContentType("all");
  };

  const articlesAfterSelection = useMemo(() =>
  {
    if (selectedContentType === "articles" || selectedContentType === "all")
    {
      return allArticles;
    }
    return [];
  }, [allArticles, selectedContentType]);

  const casesAfterSelection = useMemo(() =>
  {
    if (selectedContentType === "cases" || selectedContentType === "all")
    {
      return allCases;
    }
    return [];
  }, [allCases, selectedContentType]);

  const mainCategories = useMemo(
    () => getCountedMainCategories(allArticles ?? [], allCases ?? []),
    [allArticles, allCases]
  );

  const casesForSelectedMainCategory = useMemo(
    () => casesAfterSelection?.filter((c) => c.mainCategoryField?.[0]?.mainCategory === category),
    [casesAfterSelection, category]
  );

  const casesWithProgress = useMemo(
    () => getCasesWithProgress(casesForSelectedMainCategory ?? [], casesProgress ?? []),
    [casesForSelectedMainCategory, casesProgress]
  );

  const articlesForSelectedMainCategory = useMemo(
    () => articlesAfterSelection?.filter((a) => a.mainCategoryField?.[0]?.mainCategory === category),
    [articlesAfterSelection, category]
  );

  const articlesWithReadStatus = useMemo(
    () => getArticlesWithReadStatus(articlesForSelectedMainCategory ?? [], readArticles ?? [], seenArticles ?? []),
    [articlesForSelectedMainCategory, readArticles, seenArticles]
  );

  const matchingArticles = useMemo(
    () => getContentMatchingTheFilters(articlesWithReadStatus, filters),
    [articlesWithReadStatus, filters]
  );

  // const matchingArticles = useDeferredValue(_matchingArticles);

  const matchingCases = useMemo(
    () => getContentMatchingTheFilters(casesWithProgress, filters),
    [casesWithProgress, filters]
  );

  // const matchingCases = useDeferredValue(_matchingCases);

  const groupedContentPieces = useMemo(
    () => getGroupedContentForMainCategory(category, matchingArticles ?? [], matchingCases ?? []),
    [matchingArticles, matchingCases, category]
  );

  const filtersObject = useMemo(() => mapToObject(filters), [filters]);
  const filtersKeys = useMemo(() => objectKeys(filtersObject), [filtersObject]);

  return (
    <>
      <LibraryOverviewFiltersDrawer articles={articlesWithReadStatus} cases={casesWithProgress} />
      <OverviewHeader
        variant={"library"}
        selectedCategorySlug={category}
        // TODO: setter function wird durch neue useNavigate funktion von tanstack ersetzt
        setSelectedCategorySlug={async (slug) =>
        {
          const parsedSlug = mainCategoryKeySchema.parse(slug);
          return navigate({ search: { category: parsedSlug }, to: "/library" });
        }}
        height={400}
        categories={mainCategories}
        title={"Bibliothek"}
      />
      <div css={styles.ListWrapper}>
        <ContentWrapper stylesOverrides={styles.contentWrapper}>
          <div css={styles.filtersWrapper}>
            <div css={styles.filtersButtonRow}>
              <div css={styles.filtersButtonGroup}>
                <ContentTypeFilterBlock
                  handleContentTypeChange={setSelectedContentType}
                  selectedContentType={selectedContentType}
                />
                <Button<"button"> styleType={"secondarySimple"} onClick={openDrawer} leftIcon={<FiltersList />}>
                  Alle Filter
                  {totalFiltersCount > 0 && <span css={styles.filtersCount}>({totalFiltersCount})</span>}
                </Button>
              </div>
              <div css={styles.clearFiltersButtonWrapper}>
                <DeleteButton
                  disabled={totalFiltersCount === 0 && selectedContentType === "all"}
                  title="Alle zurücksetzen"
                  icon={<Trash />}
                  onClick={handleClearAllFilters}
                />
              </div>
            </div>
            <div css={styles.activeFiltersChips}>
              {selectedContentType !== "all" && (
                <FilterTag
                  onClick={() => setSelectedContentType("all")}
                  title={selectedContentType === "articles" ? "Nur Artikel" : "Nur Fälle"}
                />
              )}
              {filtersKeys.map((filterKey) =>
              {
                const { filterOptions, toggleFilter } = filtersObject[filterKey];

                return (
                  <Fragment key={filterKey}>
                    {filterOptions.map((filterOption) => (
                      <FilterTag
                        key={filterOption.value}
                        onClick={() => toggleFilter(filterOption)}
                        title={filterOption.label}
                      />
                    ))}
                  </Fragment>
                );
              })}
            </div>
          </div>
          <div css={styles.legalAreaWrapper}>
            {groupedContentPieces.map((legalAreaWithItems) => (
              <LegalAreaBlock
                key={legalAreaWithItems.legalArea.id}
                id={legalAreaWithItems.legalArea.id!}
                legalAreaWithItems={legalAreaWithItems}
              />
            ))}
          </div>
        </ContentWrapper>
      </div>
      <ContentWrapper>
        {totalFiltersCount > 0 && matchingArticles.length === 0 && matchingCases.length === 0 && (
          <div css={styles.noResultsWrapper}>
            <Title order={3}>Keine Ergebnisse</Title>
            <BodyText styleType="body-01-regular">
              Für deine Filter wurden leider keine Ergebnisse gefunden. Bitte ändere deine Filter.
            </BodyText>
            <Button<"button"> styleType={"primary"} onClick={handleClearAllFilters}>
              Filter zurücksetzen
            </Button>
          </div>
        )}
        {totalFiltersCount === 0 &&
          selectedContentType !== "all" &&
          matchingArticles.length === 0 &&
          matchingCases.length === 0 && (
            <div css={styles.noResultsWrapper}>
              <Title order={3}>Keine Ergebnisse</Title>
              <BodyText styleType="body-01-regular">
                Zu dem ausgewählten Inhaltstyp wurden leider keine Ergebnisse gefunden. Bitte ändere den Inhaltstyp.
              </BodyText>
              <Button<"button"> styleType={"primary"} onClick={() => setSelectedContentType("all")}>
                Beide Inhaltstypen anzeigen
              </Button>
            </div>
          )}
      </ContentWrapper>
    </>
  );
};
