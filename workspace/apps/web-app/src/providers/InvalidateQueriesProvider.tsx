/* eslint-disable max-lines */
import { searchResultsQueryKey } from "~/hooks/useSearchResults.ts";
import { api } from "~/utils/trpc.ts";

import { type AppRouter } from "@constellatio/backend/trpc";
import { useQueryClient } from "@tanstack/react-query";
import { type inferProcedureInput } from "@trpc/server";
import { createContext, type FunctionComponent, type ReactNode, useMemo } from "react";

type InvalidateDocumentsOptions = inferProcedureInput<AppRouter["documents"]["getDocuments"]>;
type InvalidateUploadedFilesOptions = inferProcedureInput<AppRouter["uploads"]["getUploadedFiles"]>;
type InvalidateFoldersOptions = inferProcedureInput<AppRouter["folders"]["getFolders"]>;
type InvalidateBookmarksOptions = inferProcedureInput<AppRouter["bookmarks"]["getAllBookmarks"]>;
type InvalidateContentItemsViewsCountOptions = inferProcedureInput<AppRouter["views"]["getContentItemViewsCount"]>;
type InvalidateCaseProgressOptions = inferProcedureInput<AppRouter["casesProgress"]["getCaseProgress"]>;
type InvalidateCasesProgressOptions = inferProcedureInput<AppRouter["casesProgress"]["getCasesProgress"]>;
type InvalidateGamesProgressOptions = inferProcedureInput<AppRouter["gamesProgress"]["getGamesProgress"]>;
type InvalidateSubmittedCaseSolutionOptions = inferProcedureInput<AppRouter["casesProgress"]["getSubmittedSolution"]>;
type InvalidateForumQuestionsOptions = inferProcedureInput<AppRouter["forum"]["getQuestions"]>;
type InvalidateForumQuestionOptions = inferProcedureInput<AppRouter["forum"]["getQuestionById"]>;
type InvalidateForumAnswersOptions = Partial<inferProcedureInput<AppRouter["forum"]["getAnswers"]>>;
type InvalidateForumAnswerOptions = inferProcedureInput<AppRouter["forum"]["getAnswerById"]>;
type InvalidateOnboardingResultOptions = inferProcedureInput<AppRouter["users"]["getOnboardingResult"]>;
type InvalidateUserDetailsResultOptions = inferProcedureInput<AppRouter["users"]["getUserDetails"]>;
type InvalidateBadgesOptions = inferProcedureInput<AppRouter["badges"]["getBadges"]>;
type InvalidateAmountOfUnreadNotificationsOptions = inferProcedureInput<
  AppRouter["notifications"]["getAmountOfUnreadNotifications"]
>;
type InvalidateNotificationOptions = inferProcedureInput<AppRouter["notifications"]["getNotificationById"]>;
type InvalidateNotificationsOptions = inferProcedureInput<AppRouter["notifications"]["getNotifications"]>;
type InvalidateSeenArticlesOptions = inferProcedureInput<AppRouter["views"]["getSeenArticles"]>;
type InvalidateReadArticlesOptions = inferProcedureInput<AppRouter["views"]["getReadArticles"]>;
type InvalidateArticleHasReadOptions = inferProcedureInput<AppRouter["views"]["getArticleHasRead"]>;
type InvalidateIsOnboardingCompletedOptions = inferProcedureInput<AppRouter["surveys"]["getIsOnboardingCompleted"]>;
type InvalidateLearningPathsOptions = inferProcedureInput<AppRouter["learningPath"]["getLearningPaths"]>;
type InvalidateAllLearningPathsProgressOptions = inferProcedureInput<
  AppRouter["learningPath"]["getAllLearningPathsProgress"]
>;
type InvalidateLearningPathProgressOptions = inferProcedureInput<AppRouter["learningPath"]["getLearningPathProgress"]>;
type InvalidateLastViewedContentItemsOptions = inferProcedureInput<AppRouter["views"]["getLastViewedContentItems"]>;
type InvalidateGetStreakOptions = inferProcedureInput<AppRouter["streak"]["getWeeklyStreak"]>;

type InvalidateQueries = {
  invalidateAllLearningPathsProgress: (options?: InvalidateAllLearningPathsProgressOptions) => Promise<void>;
  invalidateAmountOfUnreadNotifications: (options?: InvalidateAmountOfUnreadNotificationsOptions) => Promise<void>;
  invalidateArticleHasRead: (articleId: InvalidateArticleHasReadOptions) => Promise<void>;
  invalidateBadges: (options?: InvalidateBadgesOptions) => Promise<void>;
  invalidateBookmarks: (options?: InvalidateBookmarksOptions) => Promise<void>;
  invalidateCaseProgress: (options?: InvalidateCaseProgressOptions) => Promise<void>;
  invalidateCasesProgress: (options?: InvalidateCasesProgressOptions) => Promise<void>;
  invalidateContentItemsViewsCount: (options: InvalidateContentItemsViewsCountOptions) => Promise<void>;
  invalidateDocuments: (options?: InvalidateDocumentsOptions) => Promise<void>;
  invalidateEverything: () => Promise<void>;
  invalidateFolders: (options?: InvalidateFoldersOptions) => Promise<void>;
  invalidateForumAnswer: (options: InvalidateForumAnswerOptions) => Promise<void>;
  invalidateForumAnswers: (options: InvalidateForumAnswersOptions) => Promise<void>;
  invalidateForumQuestion: (options: InvalidateForumQuestionOptions) => Promise<void>;
  invalidateForumQuestions: (options?: InvalidateForumQuestionsOptions) => Promise<void>;
  invalidateGamesProgress: (options: InvalidateGamesProgressOptions) => Promise<void>;
  invalidateGetStreak: (options?: InvalidateGetStreakOptions) => Promise<void>;
  invalidateIsOnboardingCompleted: (options?: InvalidateIsOnboardingCompletedOptions) => Promise<void>;
  invalidateLastViewedContentItems: (options: InvalidateLastViewedContentItemsOptions) => Promise<void>;
  invalidateLearningPathProgress: (options: InvalidateLearningPathProgressOptions) => Promise<void>;
  invalidateLearningPaths: (options?: InvalidateLearningPathsOptions) => Promise<void>;
  invalidateNotes: () => Promise<void>;
  invalidateNotification: (options: InvalidateNotificationOptions) => Promise<void>;
  invalidateNotifications: (options?: InvalidateNotificationsOptions) => Promise<void>;
  invalidateOnboardingResult: (options?: InvalidateOnboardingResultOptions) => Promise<void>;
  invalidateReadArticles: (options?: InvalidateReadArticlesOptions) => Promise<void>;
  invalidateSearchResults: (value?: string) => Promise<void>;
  invalidateSeenArticles: (options?: InvalidateSeenArticlesOptions) => Promise<void>;
  invalidateSubmittedCaseSolution: (options: InvalidateSubmittedCaseSolutionOptions) => Promise<void>;
  invalidateSubscriptionDetails: () => Promise<void>;
  invalidateUploadedFiles: (options?: InvalidateUploadedFilesOptions) => Promise<void>;
  invalidateUserDetails: (options?: InvalidateUserDetailsResultOptions) => Promise<void>;
};

export const InvalidateQueriesContext = createContext<InvalidateQueries | null>(null);

type InvalidateQueriesProviderProps = {
  readonly children: ReactNode;
};

export const InvalidateQueriesProvider: FunctionComponent<InvalidateQueriesProviderProps> = ({ children }) =>
{
  const queryClient = useQueryClient();

  const invalidateQueries: InvalidateQueries = useMemo(
    () => ({
      invalidateAllLearningPathsProgress: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.learningPath.getAllLearningPathsProgress.queryKey(options),
        }),
      invalidateAmountOfUnreadNotifications: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.notifications.getAmountOfUnreadNotifications.queryKey(options),
        }),
      invalidateArticleHasRead: async (articleId) =>
        queryClient.invalidateQueries({
          queryKey: api.views.getArticleHasRead.queryKey(articleId),
        }),
      invalidateBadges: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.badges.getBadges.queryKey(options),
        }),
      invalidateBookmarks: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.bookmarks.getAllBookmarks.queryKey(options),
        }),
      invalidateCaseProgress: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.casesProgress.getCaseProgress.queryKey(options),
        }),
      invalidateCasesProgress: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.casesProgress.getCasesProgress.queryKey(options),
        }),
      invalidateContentItemsViewsCount: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.views.getContentItemViewsCount.queryKey(options),
        }),
      invalidateDocuments: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.documents.getDocuments.queryKey(options),
        }),
      invalidateEverything: async () => queryClient.invalidateQueries(),
      invalidateFolders: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.folders.getFolders.queryKey(options),
        }),
      invalidateForumAnswer: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.forum.getAnswerById.queryKey(options),
        }),
      invalidateForumAnswers: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.forum.getAnswers.queryKey(options),
        }),
      invalidateForumQuestion: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.forum.getQuestionById.queryKey(options),
        }),
      invalidateForumQuestions: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.forum.getQuestions.queryKey(options),
        }),
      invalidateGamesProgress: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.gamesProgress.getGamesProgress.queryKey(options),
        }),
      invalidateGetStreak: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.streak.getWeeklyStreak.queryKey(options),
        }),
      invalidateIsOnboardingCompleted: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.surveys.getIsOnboardingCompleted.queryKey(options),
        }),
      invalidateLastViewedContentItems: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.views.getLastViewedContentItems.queryKey(options),
        }),
      invalidateLearningPathProgress: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.learningPath.getLearningPathProgress.queryKey(options),
        }),
      invalidateLearningPaths: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.learningPath.getLearningPaths.queryKey(options),
        }),
      invalidateNotes: async () =>
        queryClient.invalidateQueries({
          queryKey: api.notes.getNotes.queryKey(),
        }),
      invalidateNotification: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.notifications.getNotificationById.queryKey(options),
        }),
      invalidateNotifications: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.notifications.getNotifications.queryKey(options),
        }),
      invalidateOnboardingResult: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.users.getOnboardingResult.queryKey(options),
        }),
      invalidateReadArticles: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.views.getReadArticles.queryKey(options),
        }),
      invalidateSearchResults: async (value) =>
      {
        const queryKey = [searchResultsQueryKey];

        if (value)
        {
          queryKey.push(value);
        }

        await queryClient.invalidateQueries({ queryKey });
      },
      invalidateSeenArticles: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.views.getSeenArticles.queryKey(options),
        }),
      invalidateSubmittedCaseSolution: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.casesProgress.getSubmittedSolution.queryKey(options),
        }),
      invalidateSubscriptionDetails: async () =>
        queryClient.invalidateQueries({
          queryKey: api.billing.getSubscriptionDetails.queryKey(),
        }),
      invalidateUploadedFiles: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.uploads.getUploadedFiles.queryKey(options),
        }),
      invalidateUserDetails: async (options) =>
        queryClient.invalidateQueries({
          queryKey: api.users.getUserDetails.queryKey(options),
        }),
    }),
    [queryClient]
  );

  return <InvalidateQueriesContext.Provider value={invalidateQueries}>{children}</InvalidateQueriesContext.Provider>;
};
